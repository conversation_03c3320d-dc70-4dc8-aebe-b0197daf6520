'use client';

import style from './LifeAtMtl.module.css';
import Heading from '@components/Heading';
import Image from 'next/image';
import { EmblaOptionsType } from 'embla-carousel';
import useEmblaCarousel from 'embla-carousel-react';
import AutoScroll from 'embla-carousel-auto-scroll';
import { Container } from 'react-bootstrap';
import ImageWithSizing from '@components/ImageWithSizing';

export default function LifeAtMtl({ LifeAtMtlData }) {
  const OPTIONS: EmblaOptionsType = {
    loop: true,
    dragFree: false,
    skipSnaps: false,
  };
  const [emblaRef] = useEmblaCarousel(OPTIONS, [
    AutoScroll({
      playOnInit: true,
      speed: 3,
      stopOnFocusIn: false,
      stopOnMouseEnter: false,
      stopOnInteraction: false,
    }),
  ]);
  return (
    <Container fluid className={style.sectionWrapper}>
      <div className={style.content}>
        <Heading
          className={style.title}
          title={LifeAtMtlData.title}
          headingType="h3"
        />
        <div
          className={style.description}
          dangerouslySetInnerHTML={{
            __html: LifeAtMtlData.description,
          }}
        />
      </div>
      <div className={style.embla}>
        <div className={style.embla__viewport} ref={emblaRef}>
          <div className={style.embla__container}>
            {LifeAtMtlData?.images?.data?.map((image, index) => (
              <div className={style.embla__slide} key={index}>
                <div className={style.embla__slide__number} key={index}>
                  <ImageWithSizing
                    src={image?.attributes}
                    alt={image?.attributes?.alternativeText}
                    width={400}
                    height={272}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Container>
  );
}
