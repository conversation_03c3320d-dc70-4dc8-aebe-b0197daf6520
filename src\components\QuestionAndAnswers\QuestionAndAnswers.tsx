'use client';

import { useState, useEffect } from 'react';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';
import styles from './QuestionAndAnswers.module.css';

export default function QuestionAndAnswers({
  sectionIndex,
  sectionQuestions,
  sectionData,
  sectionError,
  handleData,
  handleError,
}) {
  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  const [subAnswer, setSubAnswer] = useState(() => {
    if (localStorage.getItem('subAnswer') !== null) {
      return JSON.parse(localStorage.getItem('subAnswer'));
    }
    return [[], new Array(3).fill(null)];
  });

  // Keyboard event handler
  const handleKeyDown = (event, callback) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      callback();
    }
  };

  function handleID(i, str, id, sectionIndex, questionIndex) {
    let newSubAnswer = [...subAnswer];
    newSubAnswer[0][i] = str;
    newSubAnswer[1][i] = id;
    let value = 0;
    for (let j = 0; j < subAnswer[1].length; j++) {
      if (newSubAnswer[1][j] !== null) {
        value = value + 33.33;
      }
    }
    localStorage.setItem('subAnswer', JSON.stringify(newSubAnswer));
    setSubAnswer(newSubAnswer);
    handleError(sectionIndex, questionIndex, false);
    handleData(
      sectionIndex,
      questionIndex,
      newSubAnswer[0].join(','),
      Math.round(value),
    );
  }

  function setSubQuestion(question, sectionIndex, questionIndex) {
    const sub_question = question.sub_question;
    const answers = question.answers;
    const JSX = [];
    let answerIndex = 0;

    for (let i = 0; i < sub_question.length; i++) {
      let arr = [
        <div key={i} className={styles.sub_question_name}>
          {sub_question[i].name}
        </div>,
      ];
      for (let j = 0; j < sub_question[i].value; j++) {
        // Check if answerIndex is within bounds to prevent undefined access
        if (answerIndex >= answers.length) {
          console.warn(
            `Answer index ${answerIndex} exceeds answers array length ${answers.length}`,
          );
          break;
        }

        // Capture the current answerIndex value to avoid closure issues
        const currentAnswerIndex = answerIndex;
        const currentAnswer = answers[currentAnswerIndex];

        let content = (
          <label
            key={currentAnswer.id}
            className={
              subAnswer[1][i] === currentAnswer.id
                ? ` ${styles.mcq} ${styles.selected_mcq}`
                : styles.mcq
            }
            htmlFor={currentAnswer.id}
            tabIndex={0}
            onKeyDown={e =>
              handleKeyDown(e, () => {
                handleID(
                  i,
                  currentAnswer.name,
                  Number(currentAnswer.id),
                  sectionIndex,
                  questionIndex,
                );
              })
            }
          >
            <input
              type="radio"
              id={currentAnswer.id}
              name={sub_question[i].name}
              data-name={currentAnswer.name}
              value={currentAnswer.id}
              onChange={e => {
                handleID(
                  i,
                  e.target.dataset.name,
                  Number(e.target.value),
                  sectionIndex,
                  questionIndex,
                );
              }}
            />
            {currentAnswer.name}
          </label>
        );
        answerIndex = answerIndex + 1;
        arr.push(content);
      }
      JSX.push(arr);
    }
    return JSX;
  }

  function getAnswerName(questionIndex, value) {
    let ind = Math.round((value as any) / 25);
    // Check if the calculated index is within bounds
    if (ind >= sectionQuestions[questionIndex].answers.length) {
      console.warn(
        `Answer index ${ind} exceeds answers array length ${sectionQuestions[questionIndex].answers.length}`,
      );
      return '';
    }
    return sectionQuestions[questionIndex].answers[ind].name;
  }

  return (
    <>
      {sectionQuestions.map((question, questionIndex) => (
        <div key={questionIndex} className={styles.container}>
          <div className={styles.question_container}>
            <div
              className={
                sectionError[questionIndex]
                  ? `${styles.question_number} ${styles.error_message}`
                  : styles.question_number
              }
            >
              {question.number < 10 ? '0' : ''}
              {question.number}
              {'.'}
            </div>
            <div className={styles.question_name}>{question.name}</div>
          </div>
          {question.type === 'mcq' ? (
            <div className={styles.mcqs_container}>
              {question.sub_question.length !== 0 ? (
                <>{setSubQuestion(question, sectionIndex, questionIndex)}</>
              ) : (
                <>
                  {question.answers.map((ans, answerIndex) => (
                    <label
                      key={answerIndex}
                      className={
                        sectionData[questionIndex][1] === ans.value
                          ? ` ${styles.mcq} ${styles.selected_mcq}`
                          : styles.mcq
                      }
                      htmlFor={ans.id}
                      tabIndex={0}
                      onKeyDown={e =>
                        handleKeyDown(e, () => {
                          handleError(sectionIndex, questionIndex, false);
                          handleData(
                            sectionIndex,
                            questionIndex,
                            ans.name,
                            Number(ans.value),
                          );
                        })
                      }
                    >
                      <input
                        type="radio"
                        id={ans.id}
                        name={question.name}
                        value={ans.value}
                        onChange={e => {
                          handleError(sectionIndex, questionIndex, false);
                          handleData(
                            sectionIndex,
                            questionIndex,
                            ans.name,
                            Number(e.target.value),
                          );
                        }}
                      />
                      {ans.name}
                    </label>
                  ))}
                </>
              )}
            </div>
          ) : (
            <>
              {!isTablet ? (
                <div className={styles.draggable_container}>
                  <input
                    type="range"
                    id="range"
                    name="range"
                    min="0"
                    max="100"
                    step="25"
                    className={styles.draggable_input}
                    value={sectionData[questionIndex][1]}
                    onChange={e => {
                      handleError(sectionIndex, questionIndex, false);
                      handleData(
                        sectionIndex,
                        questionIndex,
                        getAnswerName(questionIndex, e.target.value),
                        Number(e.target.value),
                      );
                    }}
                    style={{
                      background: `linear-gradient(to right, #30AD43 0%, #30AD43 ${sectionData[questionIndex][1]}%, #ccc ${sectionData[questionIndex][1]}%, #ccc 100%)`,
                    }}
                  />
                  <div className={styles.draggable_wrapper}>
                    {question.answers.map((ans, answerIndex) => (
                      <div
                        key={answerIndex}
                        className={
                          sectionData[questionIndex][1] === ans.value
                            ? `${styles.draggable_label} ${styles.selected_draggable_label}`
                            : styles.draggable_label
                        }
                        onClick={e =>
                          handleData(
                            sectionIndex,
                            questionIndex,
                            ans.name,
                            Number(ans.value),
                          )
                        }
                        onKeyDown={e =>
                          handleKeyDown(e, () => {
                            handleData(
                              sectionIndex,
                              questionIndex,
                              ans.name,
                              Number(ans.value),
                            );
                          })
                        }
                        tabIndex={0}
                        role="button"
                        aria-pressed={
                          sectionData[questionIndex][1] === ans.value
                        }
                      >
                        {ans.name}
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className={styles.number_wrapper}>
                  <div className={styles.draggable_container_tablet}>
                    {question.answers.map((ans, answerIndex) => (
                      <label
                        key={answerIndex}
                        className={
                          sectionData[questionIndex][1] === ans.value
                            ? ` ${styles.number} ${styles.selected_number}`
                            : styles.number
                        }
                        htmlFor={ans.id}
                        tabIndex={0}
                        onKeyDown={e =>
                          handleKeyDown(e, () => {
                            handleError(sectionIndex, questionIndex, false);
                            handleData(
                              sectionIndex,
                              questionIndex,
                              ans.name,
                              Number(ans.value),
                            );
                          })
                        }
                      >
                        <input
                          type="radio"
                          id={ans.id}
                          name={question.name}
                          value={ans.value}
                          onChange={e => {
                            handleError(sectionIndex, questionIndex, false);
                            handleData(
                              sectionIndex,
                              questionIndex,
                              ans.name,
                              Number(e.target.value),
                            );
                          }}
                        />
                        {answerIndex + 1}
                      </label>
                    ))}
                  </div>
                  <div className={styles.number_label}>
                    <span>{question.answers[0].name}</span>
                    <span>
                      {question.answers[question.answers.length - 1].name}
                    </span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      ))}
    </>
  );
}
