@value variables: "@styles/variables.module.css";
@value grayBorder, gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md-820, breakpoint-xl-1400 from breakpoints;

.container {
  display: flex;
  justify-content: center;
  padding: 80px 16px;
  flex-direction: column;
  align-items: center;

  @media screen and (max-width: breakpoint-md-820) {
    padding: 40px 16px;
  }

  background-color: gray300;
}

.cardWrapper {
  display: flex;
  justify-content: center;
  max-width: 1192px;
  flex-wrap: wrap;
  row-gap: 40px;
  column-gap: 20px;
}

.videoCard {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 384px;

  @media screen and (max-width: breakpoint-md-820) {
    max-width: 342px;
  }
}

.tagName {
  background-color: grayBorder;
  color: colorWhite;
  max-width: fit-content;
  padding: 6px 10px;
  border-radius: 6px;
}

.videoTitle {
  font-weight: 600;
  font-size: 18px;
  line-height: 140%;
}

.videoContainer {
  position: relative;
  height: 220px;
}

.thumbnail {
  background-color: colorBlack;
  object-fit: cover;
  border-radius: 6px;
}

.thumbnail:hover {
  cursor: pointer;
}

.playButton {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.playButton:hover {
  cursor: pointer;
}

.card_title>h3 {
  color: colorBlack;

  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
  max-width: 384px;

  @media screen and (max-width: breakpoint-md-820) {
    max-width: 342px;
  }
}

.filter_container {
  display: flex;
  justify-content: left;
  gap: 40px;
  flex-direction: column;
  padding: 80px 120px 20px 120px;

  @media screen and (max-width: breakpoint-xl-1400) {
    padding: 40px;
  }

  background-color: #f3f3f3;
}

.filter_content {
  display: flex;
  justify-content: space-between;
}

.filter_title>h2 {
  color: colorBlack;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
}

.filter_tabs {
  display: flex;
  justify-content: space-between;
}

.dropdown_tabs {
  position: relative;
}

.dropdown_container {
  display: flex;
}

.dropdown_button {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  cursor: pointer;
}

.dropdown_content {
  position: absolute;
  z-index: 2;
  background-color: colorWhite;
  border-radius: 6px;
  min-width: 256px;
  top: 67px;
  font-size: 18px;
  max-height: 310px;
  overflow-y: auto;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
}

.dropdown_content::-webkit-scrollbar {
  width: 2px;
}

.dropdown_content::-webkit-scrollbar-thumb {
  background-color: #b8b4b4;
  border-radius: 8px;
}

.dropdown_content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown_content li {
  padding: 16px 16px 0 16px;
  cursor: pointer;
}

.dropdown_content li:hover {
  background-color: #f1f1f1;
}

.dropdown_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  width: 256px;
  padding: 15px 22px;
  background-color: colorWhite;
  border-radius: 6px;
}

.selected_value_container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected_value_box {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  background-color: #e4e4e4;
  position: relative;
  padding: 10px;
  border-radius: 6px;
}

.close_icon {
  margin-left: 12px;
  cursor: pointer;
}

.checkbox_label {
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #8c8b8b;
  padding-bottom: 16px;
  cursor: pointer;
}

.clear_button {
  background-image: linear-gradient(gray300, gray300),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%) !important;
  color: colorBlack !important;
  font-size: 20px !important;
  font-style: normal !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

.button_filter_mobile {
  display: flex;
  justify-content: space-between;
  padding: 40px 32px;
  background-color: #f3f3f3;
}

.filter_button_mobile {
  height: 62px;
}

.sortButton {
  cursor: pointer;
  font-size: 20px;
  line-height: 160%;
  gap: 5px;
  display: flex;
  align-items: center;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination {
  display: flex;
  list-style-type: none;
  gap: 30px;
  padding: 0;
  align-items: center;
  color: colorBlack;
  flex-direction: row;
  list-style-image: none;
}

.paginationLink {
  cursor: pointer;
  color: colorBlack;
  text-decoration: none;
}

.paginationDisabled {
  color: #ccc;
  cursor: not-allowed;
  text-decoration: none;
}

.paginationActive {
  background-color: #b41f5e;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50px;
}

.arrowImage {
  rotate: 180deg;
}

.filterBtnWrapper {
  display: flex;
  flex-direction: row;
  gap: 30px;
}

.no_data_found {
  font-size: 18px;
  font-weight: 500;
}