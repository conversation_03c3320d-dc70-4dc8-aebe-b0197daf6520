import React from 'react';
import CaseStudyContent from '@components/Wrappers/CaseStudyContent';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export const dynamic = 'force-static';

export async function generateStaticParams() {
  const response = await fetchFromStrapi('case-studies');
  const caseStudy = response.data.map(res => ({
    caseStudy: res.attributes.slug,
  }));
  return caseStudy;
}

export async function fetchCaseStudyData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=hero_section.download_button,hero_section.global_industries,hero_section.global_services,hero_section.image,richText_for_TheClient,richText_for_Challenges,richText_for_Solutions.background_variant,richText_for_Quote,richText_for_Results,quote,caseStudy_form.form_values,caseStudy_form.form_download_button,seo.schema`;
  return await fetch<PERSON><PERSON><PERSON><PERSON><PERSON>('case-studies', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { caseStudy: string };
}) {
  const queryString = `filters[slug][$eq]=${params.caseStudy}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi('case-studies', queryString);
  const seoData = seoFetchedData?.data?.[0]?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function Services({ params }: { params: any }) {
  const { caseStudy } = params;
  const caseStudyData = await fetchCaseStudyData(caseStudy);

  // Check if case-study page data exists, otherwise return 404
  if (!caseStudyData?.data || caseStudyData?.data.length === 0) {
    notFound();
  }

  return <CaseStudyContent data={caseStudyData} />;
}
