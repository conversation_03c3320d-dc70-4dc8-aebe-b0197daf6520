'use client';

import React, { useEffect, useState } from 'react';
import styles from '@components/Header/Header.module.css';
import Link from '@components/Link/Link';
import classNames from '@utils/classNames';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function MenuLink({
  linkTitle,
  href,
  onClick,
  fromSevices = false,
}: any) {
  const [isRendered, setIsRendered] = useState(false);
  useEffect(() => {
    setIsRendered(true);
  }, []);

  if (!isRendered) {
    return null;
  }

  return (
    <div className={styles.linkWrapper}>
      <Link
        className={classNames(
          fromSevices === true ? styles.link : styles.linkTitle_others,
        )}
        href={href}
        onClick={onClick}
      >
        {linkTitle}
      </Link>
    </div>
  );
}
