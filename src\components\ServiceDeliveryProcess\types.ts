export interface ServiceDeliveryProcess {
  id: number;
  description: string;
  service_page_link: string;
  title: string;
  on_hover_bg_image: {
    data: {
      id: number;
      attributes: {
        alternativeText?: string;
        caption?: string;
        createdAt?: string;
        ext?: string;
        formats?: null;
        hash?: string;
        height?: number;
        mime?: string;
        name?: string;
        previewUrl?: null;
        provider?: string;
        provider_metadata?: null;
        size?: number;
        updatedAt?: string;
        url?: string;
        width?: number;
      };
    };
  };
}

export interface ServiceDeliveryProcessTypes {
  data: {
    id: number;
    title: string;
    all_services_card: {
      id: number;
      link: string;
      title: string;
    };
    other_services_card: ServiceDeliveryProcess[];
  };
}
