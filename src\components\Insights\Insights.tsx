'use client';

import { Container } from 'react-bootstrap';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import CircularTagline from '@components/CircularTagline';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import Link from '@components/Link';
import NextButton from '@components/SliderButtons/NextButton';
import PrevButton from '@components/SliderButtons/PrevButton';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import usePrevNextButtons from '@hooks/usePrevNextButtons';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import emblastyles from '../../styles/emlaDots.module.css';
import styles from './Insights.module.css';
import { InsightsSliderTypes, InsightsTypes } from './types';

export default function Insights({ data, variantWhite = true }: InsightsTypes) {
  const insightsSlider = data?.blogs?.data;
  const [emblaRefForImage, emblaApiForImage] = useEmblaCarousel({
    direction: 'ltr',
    watchDrag(emblaApi) {
      emblaApi.off('slidesChanged', () => {});
    },
  });
  const [emblaRefForTexts, emblaApiForTexts] = useEmblaCarousel({
    direction: 'ltr',
    watchDrag(emblaApi) {
      emblaApi.off('slidesChanged', () => {});
    },
  });

  const { onDotButtonClick: onImageDotButtonClick } =
    useDotButton(emblaApiForImage);

  const {
    selectedIndex: selectedIndexTexts,
    scrollSnaps: scrollSnapsTexts,
    onDotButtonClick: onTextDotButtonClick,
  } = useDotButton(emblaApiForTexts);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApiForTexts);

  const {
    onPrevButtonClick: onImgPrevButtonClick,
    onNextButtonClick: onImgNextButtonClick,
  } = usePrevNextButtons(emblaApiForImage);

  const onPrev = () => {
    onPrevButtonClick();
    onImgPrevButtonClick();
  };

  const onNext = () => {
    onNextButtonClick();
    onImgNextButtonClick();
  };

  const onDotButtonClick = index => {
    onTextDotButtonClick(index);
    onImageDotButtonClick(index);
  };

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md-769']})`,
  });

  return (
    <section className={styles.insightSection}>
      <Container fluid className={styles.insightContainer}>
        <div className={styles.insightsHeadingArea}>
          <div className={styles.titleAndSubtitle}>
            <Heading headingType="h2" title={data?.title} />
            <span
              className={styles.subtitle}
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{ __html: data?.subtitle }}
            />
          </div>
          <div>
            <CircularTagline
              tagLineHeight={120}
              taglineWidth={120}
              arrowHeight="35"
              arrowWidth="35"
              textColor="black"
              href={data?.taglineUrl}
              text_url={data?.circular_text_image?.data.attributes.url}
            />
          </div>
        </div>
        {insightsSlider && (
          <div className={styles.insightsSlider}>
            <div className={styles.embla}>
              <div className={styles.embla__viewport} ref={emblaRefForImage}>
                <div
                  className={classNames(
                    styles.embla__container,
                    styles.sliderImage,
                  )}
                >
                  {insightsSlider?.map((slider: InsightsSliderTypes) => (
                    <div className={styles.embla__slide} key={slider?.id}>
                      <Image
                        src={
                          slider?.attributes?.heroSection_image?.data
                            ?.attributes?.format?.medium?.url ||
                          slider?.attributes?.heroSection_image?.data
                            ?.attributes?.formats?.medium?.url ||
                          slider?.attributes?.heroSection_image?.data
                            ?.attributes?.formats?.large?.url ||
                          slider?.attributes?.heroSection_image?.data
                            ?.attributes?.format?.large?.url ||
                          slider?.attributes?.heroSection_image?.data
                            ?.attributes?.url
                        }
                        alt={
                          slider?.attributes?.heroSection_image?.data
                            ?.attributes?.alternativeText
                        }
                        className={styles.image}
                        fill
                        quality={90}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className={styles.sliderInfo}>
              <div className={styles.embla_texts}>
                <div
                  className={styles.embla__viewport__texts}
                  ref={emblaRefForTexts}
                >
                  <div className={styles.embla__container__texts}>
                    {insightsSlider?.map((slider: InsightsSliderTypes) => (
                      <div
                        className={styles.embla__slide__texts}
                        key={slider?.id}
                      >
                        <Heading
                          headingType="h3"
                          title={slider?.attributes?.title}
                        />
                        <span
                          className={styles.subtitle}
                          // eslint-disable-next-line react/no-danger
                          dangerouslySetInnerHTML={{
                            __html: slider?.attributes?.description,
                          }}
                        />
                        <Link
                          href={`/${slider?.attributes?.slug}`}
                          className={styles.viewMoreLink}
                        >
                          View More
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
                <div className={styles.embla__controls}>
                  <div className={emblastyles.embla__dots}>
                    {scrollSnapsTexts.length > 1 &&
                      scrollSnapsTexts.map((_, index) => (
                        <DotButton
                          key={index}
                          onClick={() => onDotButtonClick(index)}
                          className={
                            index === selectedIndexTexts
                              ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                              : variantWhite
                                ? classNames(
                                    emblastyles.embla__dot,
                                    emblastyles.embla__dot_bg_white,
                                  )
                                : emblastyles.embla__dot
                          }
                        />
                      ))}
                  </div>
                  {!isTablet && scrollSnapsTexts.length > 1 && (
                    <div className={styles.embla__buttons}>
                      <PrevButton onClick={onPrev} disabled={prevBtnDisabled} />
                      <NextButton onClick={onNext} disabled={nextBtnDisabled} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </Container>
    </section>
  );
}
