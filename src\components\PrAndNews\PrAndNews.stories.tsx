import PrAndNews from './PrAndNews';

export default {
  title: 'Components/CTA',
};

const data = {
  data: [
    {
      id: 1,
      attributes: {
        title: 'test PR one',
        createdAt: '2024-08-09T09:22:22.484Z',
        updatedAt: '2024-08-09T09:56:36.531Z',
        publishedAt: '2024-08-09T09:22:32.427Z',
        slug: 'test-pr-one',
        image: {
          data: {
            id: 46,
            attributes: {
              name: 'image 100 (2).png',
              alternativeText: 'test 1',
              caption: null,
              width: 387,
              height: 300,
              formats: {
                thumbnail: {
                  name: 'thumbnail_image 100 (2).png',
                  hash: 'thumbnail_image_100_2_3995aeeb1f',
                  ext: '.png',
                  mime: 'image/png',
                  path: null,
                  width: 201,
                  height: 156,
                  size: 73.7,
                  sizeInBytes: 73700,
                  url: 'https://cdn.marutitech.com/thumbnail_image_100_2_3995aeeb1f.png',
                },
              },
              hash: 'image_100_2_3995aeeb1f',
              ext: '.png',
              mime: 'image/png',
              size: 47.05,
              url: 'https://cdn.marutitech.com/image_100_2_3995aeeb1f.png',
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-19T09:31:41.363Z',
              updatedAt: '2024-08-09T09:23:05.393Z',
            },
          },
        },
      },
    },
    {
      id: 2,
      attributes: {
        title: 'Test PR two',
        createdAt: '2024-08-09T09:22:47.889Z',
        updatedAt: '2024-08-09T09:56:46.373Z',
        publishedAt: '2024-08-09T09:22:48.542Z',
        slug: 'test-pr-two',
        image: {
          data: {
            id: 45,
            attributes: {
              name: 'image 100 (3).png',
              alternativeText: 'test 2',
              caption: null,
              width: 387,
              height: 300,
              formats: {
                thumbnail: {
                  name: 'thumbnail_image 100 (3).png',
                  hash: 'thumbnail_image_100_3_6958c20039',
                  ext: '.png',
                  mime: 'image/png',
                  path: null,
                  width: 201,
                  height: 156,
                  size: 61.14,
                  sizeInBytes: 61139,
                  url: 'https://cdn.marutitech.com/thumbnail_image_100_3_6958c20039.png',
                },
              },
              hash: 'image_100_3_6958c20039',
              ext: '.png',
              mime: 'image/png',
              size: 45.49,
              url: 'https://cdn.marutitech.com/image_100_3_6958c20039.png',
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-19T09:31:41.276Z',
              updatedAt: '2024-08-09T09:23:29.102Z',
            },
          },
        },
      },
    },
    {
      id: 3,
      attributes: {
        title: 'Three',
        createdAt: '2024-08-09T10:19:11.261Z',
        updatedAt: '2024-08-09T10:19:12.223Z',
        publishedAt: '2024-08-09T10:19:12.220Z',
        slug: 'three',
        image: {
          data: {
            id: 44,
            attributes: {
              name: 'image 100 (1).png',
              alternativeText: 'test 3',
              caption: null,
              width: 387,
              height: 300,
              formats: {
                thumbnail: {
                  name: 'thumbnail_image 100 (1).png',
                  hash: 'thumbnail_image_100_1_0d127c1ae2',
                  ext: '.png',
                  mime: 'image/png',
                  path: null,
                  width: 201,
                  height: 156,
                  size: 63.74,
                  sizeInBytes: 63737,
                  url: 'https://cdn.marutitech.com/thumbnail_image_100_1_0d127c1ae2.png',
                },
              },
              hash: 'image_100_1_0d127c1ae2',
              ext: '.png',
              mime: 'image/png',
              size: 51.5,
              url: 'https://cdn.marutitech.com/image_100_1_0d127c1ae2.png',
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-19T09:31:40.132Z',
              updatedAt: '2024-06-19T09:32:03.411Z',
            },
          },
        },
      },
    },
  ],
  meta: {
    pagination: {
      page: 1,
      pageSize: 25,
      pageCount: 1,
      total: 3,
    },
  },
};

export function PrAndNewsStory() {
  return (
    <div>
      <PrAndNews data={data.data} />
    </div>
  );
}
