const date = new Date();

const currentTimestamp = () => {
  const formatData = input => {
    if (input > 9) {
      return input;
    } else {
      return `0${input}`;
    }
  };

  let dd = formatData(date.getDate());
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  let month = monthNames[date.getMonth()];
  let yyyy = date.getFullYear();
  let HH = formatData(date.getHours());
  let MM = formatData(date.getMinutes());
  let SS = formatData(date.getSeconds());

  return `---> NEW LEAD ALERT : ${dd}/${month}/${yyyy} ${HH}:${MM}:${SS}`;
};

export default currentTimestamp;
