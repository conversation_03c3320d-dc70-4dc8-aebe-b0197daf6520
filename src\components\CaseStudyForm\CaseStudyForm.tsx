'use client';

import React, { useState } from 'react';
import { Container } from 'react-bootstrap';
import Button from '@components/Button';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import styles from './CaseStudyForm.module.css';
import useForm from '@hooks/useForm';
import getUserLocation from '@utils/getUserLocation';

const CaseStudyForm = React.forwardRef(
  ({ formData, source = 'HomePage' }: any, ref: any) => {
    const {
      download_title,
      description,
      form_download_button: { title, link },
      form_values: {
        fieldNameFor_FirstName,
        fieldNameFor_EmailAddress,
        fieldNameFor_CompanyName,
        fieldNameFor_PhoneNumber,
      },
    } = formData;

    const [isSubmitting, setIsSubmitting] = useState(false);
    const userCountryCode = getUserLocation();

    const { values, errors, errorMessages, handleChange, handleSubmit } =
      useForm(
        {
          firstName: '',
          emailAddress: '',
          phoneNumber: '',
          companyName: '',
          consent: false,
        },
        {
          firstName: { empty: false },
          emailAddress: { empty: false, invalid: false },
          phoneNumber: { empty: false, invalid: false },
          companyName: { empty: false },
          consent: { empty: false },
        },
        'caseStudy',
        source,
        link,
      );

    const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();

      setIsSubmitting(true);

      try {
        await handleSubmit(event);
      } catch (error) {
        console.error('Form submission failed:', error);
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <Container ref={ref} fluid className={styles.CaseStudyFormContainer}>
        <div className={styles.contentWrapper}>
          <h2 className={styles.downloadTitle}>{download_title}</h2>
          <div className={styles.description}>{description}</div>
        </div>
        <form className={styles.form} onSubmit={onSubmit}>
          <label
            className={
              errors.firstName.empty ? styles.errorLabel : styles.formFields
            }
          >
            {fieldNameFor_FirstName}*{' '}
          </label>
          <input
            className={
              errors.firstName.empty
                ? `${styles.errorInput} ${styles.formInput} `
                : `${styles.formInput}`
            }
            placeholder="Full Name"
            type="text"
            value={values.firstName}
            id="firstName"
            name="firstName"
            maxLength={50}
            onChange={e => handleChange(e?.target)}
            onBlur={e => handleChange(e?.target)}
          />
          <label
            className={
              errors.emailAddress.empty || errors.emailAddress.invalid
                ? styles.errorLabel
                : styles.formFields
            }
          >
            {fieldNameFor_EmailAddress}*
          </label>
          <input
            className={
              errors.emailAddress.empty
                ? `${styles.errorInput} ${styles.formInput}`
                : `${styles.formInput}`
            }
            type="text"
            id="emailAddress"
            name="emailAddress"
            placeholder="Your Email ID"
            value={values.emailAddress}
            maxLength={50}
            onChange={e => handleChange(e?.target)}
            onBlur={e => handleChange(e?.target)}
          />
          <label
            className={
              errors.phoneNumber.empty || errors.phoneNumber.invalid
                ? styles.errorLabel
                : styles.formFields
            }
          >
            {fieldNameFor_PhoneNumber}*
          </label>

          <PhoneInput
            inputClass={
              errors.phoneNumber.empty || errors.phoneNumber.invalid
                ? `${styles.errorInput} ${styles.ph_number_countries_input_services_page}`
                : styles.ph_number_countries_input_services_page
            }
            buttonClass={
              errors.phoneNumber.empty || errors.phoneNumber.invalid
                ? `${styles.errorInput} ${styles.ph_number_countries_button_services_page}`
                : styles.ph_number_countries_button_services_page
            }
            dropdownClass={styles.ph_number_countries_dropdown_services_page}
            preferredCountries={[
              'us',
              'gb',
              'sg',
              'de',
              'sa',
              'in',
              'nl',
              'au',
              'be',
              'my',
            ]}
            country={userCountryCode || 'us'}
            placeholder="Your Phone Number"
            value={values.phoneNumber}
            onChange={value => handleChange({ value, name: 'phoneNumber' })}
            onBlur={e => handleChange(e?.target)}
          />
          <label className={styles.formFields}>
            {fieldNameFor_CompanyName}
          </label>
          <input
            className={styles.formInput}
            placeholder="Your Company Name"
            type="text"
            id="companyName"
            name="companyName"
            value={values.companyName}
            maxLength={50}
            onChange={e => handleChange(e?.target)}
          />
          {isSubmitting ? (
            <div className={styles.container_spinner}>
              <div className={styles.spinner}></div>
            </div>
          ) : (
            <Button
              className={styles.submitButton}
              label={title}
              type="submit"
            />
          )}
          <div className={styles.errorMessages}>
            <div>{errorMessages.empty && errorMessages.empty}</div>
            <div>{errorMessages.invalid && errorMessages.invalid}</div>
          </div>
        </form>
      </Container>
    );
  },
);

export default CaseStudyForm;
