@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.container {
  padding: 36px 0 80px 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 36px 0 36px 0;
  }
}

.step_container {
  height: 45px;
  padding: 16px;
  display: flex;
  gap: 10px;

  font-weight: 500;
  font-size: 18px;
  line-height: 160%;
  letter-spacing: 0.36;
}

.hidden {
  display: none;
}

.section_wrapper {
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 0 10px;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.heading>h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px;
  border-bottom: 2px solid black;
}

.error_message {
  padding: 20px;
  font-weight: 500;
  font-size: 26px;
  line-height: 164%;
  color: #ff0000;
}

.button_wrapper_mobile {
  display: flex;
  gap: 5px;
  font-weight: 500;
  font-size: 18px;
  padding: 0 20px;
}

.button_wrapper {
  display: flex;
  justify-content: flex-end;
  gap: 40px;
}

.result_button {
  padding: 13px 35.5px;
  border-radius: 3px;
  border-width: 2px;

  font-weight: 600;
  font-size: 20px;
  line-height: 148%;

  color: colorWhite;
  background: linear-gradient(90deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
}

.tags {
  text-align: center;
}

.result_section {
  max-width: 1192px;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
  }
}

.restart_button,
.consultation_button {
  display: flex !important;
  gap: 10px !important;

  padding: 13px 35.5px !important;
  border-radius: 3px !important;
  border-width: 2px !important;

  font-weight: 600 !important;
  font-size: 16px !important;

  color: colorBlack !important;
  background-image: linear-gradient(colorWhite, colorWhite),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%) !important;
}

.description,
.consultation_button {
  margin: 0 auto !important;
}

.score_cards_wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  @media screen and (max-width: 1192px) {
    justify-content: center;
  }
}

.score_cards {
  background-color: gray300;
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 384px;
  border-radius: 12px;
  padding: 20px;

  font-size: 20px;
  line-height: 160%;
}

.gauge_wrapper {
  display: flex;
  justify-content: center;
}