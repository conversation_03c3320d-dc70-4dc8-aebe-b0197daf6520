'use client';

import React, { useState } from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Button from '@components/Button';
import classNames from '@utils/classNames';
import Heading from '@components/Heading';

import styles from './AIReadinessForm.module.css';
import useForm from '@hooks/useForm';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import getUserLocation from '@utils/getUserLocation';

export default function AIReadinessForm({
  formData,
  source = 'AIReadiness',
  handleResult,
  handleVisibleSection,
}: any) {
  const {
    title,
    instructions,
    consent_statement,
    LinkedInButton_title,
    button,
    formFields: {
      fieldNameFor_FirstName,
      fieldNameFor_LastName,
      fieldNameFor_EmailAddress,
      fieldNameFor_CompanyName,
      fieldNameFor_PhoneNumber,
      fieldNameFor_HowCanWeHelpYou,
    },
  } = formData;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmitAIReadiness,
  } = useForm(
    {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
      howDidYouHearAboutUs: '',
      companyName: '',
      howCanWeHelpYou: '',
      consent: false,
    },
    {
      firstName: {
        empty: false,
      },
      lastName: {
        empty: false,
      },
      emailAddress: {
        empty: false,
        invalid: false,
      },
      phoneNumber: {
        empty: false,
        invalid: false,
      },
      consent: {
        empty: false,
      },
    },
    'default',
    source,
  );
  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    try {
      let { data, newResult } = handleResult();

      await handleSubmitAIReadiness(data, newResult, handleVisibleSection);
    } catch (error) {
      console.error('Form submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <Container fluid>
      <div className={styles.formWrapper}>
        <Heading
          title="Contact Details"
          headingType="h2"
          className={styles.heading}
        />
        <form className={styles.form} onSubmit={onSubmit}>
          <div className={styles.formFields}>
            <div className={styles.personalDetailsWrapper}>
              <div className={classNames(styles.row, styles.firstRow)}>
                <div className={classNames(styles.row, styles.nameFields)}>
                  <div className={classNames(styles.nameAndInputWrapper)}>
                    <label
                      htmlFor="firstName"
                      className={
                        errors.firstName.empty
                          ? styles.errorLabel
                          : styles.formLabel
                      }
                    >
                      {fieldNameFor_FirstName}*
                    </label>
                    <input
                      className={
                        errors.firstName.empty
                          ? `${styles.errorInput} ${styles.formInput}`
                          : `${styles.formInput}`
                      }
                      type="text"
                      id="firstName"
                      name="firstName"
                      maxLength={50}
                      value={values.firstName}
                      onChange={e => handleChange(e?.target)}
                      onBlur={e => handleChange(e?.target)}
                    />
                  </div>
                  <div className={classNames(styles.nameAndInputWrapper)}>
                    <label
                      htmlFor="lastName"
                      className={
                        errors.lastName.empty
                          ? styles.errorLabel
                          : styles.formLabel
                      }
                    >
                      {fieldNameFor_LastName}*
                    </label>
                    <input
                      className={
                        errors.lastName.empty
                          ? `${styles.errorInput} ${styles.formInput}`
                          : `${styles.formInput}`
                      }
                      type="text"
                      id="lastName"
                      name="lastName"
                      maxLength={50}
                      value={values.lastName}
                      onChange={e => handleChange(e?.target)}
                      onBlur={e => handleChange(e?.target)}
                    />
                  </div>
                  <div className={styles.nameAndInputWrapper}>
                    <label
                      htmlFor="emailAddress"
                      className={
                        errors.emailAddress.empty || errors.emailAddress.invalid
                          ? styles.errorLabel
                          : styles.formLabel
                      }
                    >
                      {fieldNameFor_EmailAddress}*
                    </label>
                    <input
                      className={
                        errors.emailAddress.empty
                          ? `${styles.errorInput} ${styles.formInput}`
                          : `${styles.formInput}`
                      }
                      type="text"
                      id="emailAddress"
                      name="emailAddress"
                      maxLength={50}
                      value={values.emailAddress}
                      onChange={e => handleChange(e?.target)}
                      onBlur={e => handleChange(e?.target)}
                    />
                  </div>

                  <div className={styles.nameAndInputWrapper}>
                    <label
                      htmlFor="phoneNumber"
                      className={
                        errors.phoneNumber.empty || errors.phoneNumber.invalid
                          ? styles.errorLabel
                          : styles.formLabel
                      }
                    >
                      {fieldNameFor_PhoneNumber}*
                    </label>
                    <div className={styles.phoneInputWrapper}>
                      <PhoneInput
                        inputProps={{ id: 'phoneNumber' }}
                        placeholder=""
                        inputClass={
                          errors.phoneNumber.empty || errors.phoneNumber.invalid
                            ? `${styles.errorInput} ${styles.formInputPhone}`
                            : styles.formInputPhone
                        }
                        buttonClass={
                          errors.phoneNumber.empty || errors.phoneNumber.invalid
                            ? `${styles.errorInput} ${styles.formInputPhone_dial_icon}`
                            : styles.formInputPhone_dial_icon
                        }
                        dropdownClass={styles.ph_number_countries_dropdown}
                        preferredCountries={[
                          'us',
                          'gb',
                          'sg',
                          'de',
                          'sa',
                          'in',
                          'nl',
                          'au',
                          'be',
                          'my',
                        ]}
                        country={userCountryCode || 'us'}
                        value={values.phoneNumber}
                        onChange={value =>
                          handleChange({ value, name: 'phoneNumber' })
                        }
                        onBlur={e => handleChange(e?.target)}
                      />
                    </div>
                  </div>
                  <div
                    className={classNames(
                      styles.nameAndInputWrapper,
                      styles.companyNameWrapper,
                    )}
                  >
                    <label htmlFor="companyName" className={styles.formLabel}>
                      {fieldNameFor_CompanyName}
                    </label>
                    <input
                      className={styles.formInput}
                      type="text"
                      id="companyName"
                      name="companyName"
                      maxLength={50}
                      value={values.companyName}
                      onChange={e => handleChange(e?.target)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.consentRow}>
            <label
              className={
                errors.consent.empty
                  ? `${styles.errorLabel_consentText} ${styles.consentText}`
                  : styles.consentText
              }
              htmlFor="consent"
              onClick={() => {
                handleChange({
                  name: 'consent',
                  type: 'checkbox',
                  value: '',
                  checked: !values.consent,
                });
              }}
            >
              <input
                type="checkbox"
                id="consent"
                name="consent"
                checked={values.consent}
              />
              <span>{consent_statement}</span>
            </label>
          </div>
          <div className={styles.submitButtonRow}>
            {isSubmitting ? (
              <div className={styles.container_spinner}>
                <div className={styles.spinner}></div>
              </div>
            ) : (
              <Button
                type="submit"
                className={styles.result_button}
                label="Check my AI Readiness Score"
              />
            )}

            {LinkedInButton_title && (
              <a className={styles.linkedInButton} href="#">
                {LinkedInButton_title}
                <Image
                  src="https://cdn.marutitech.com/linkedin_c13ca9a536.png"
                  width={32}
                  height={32}
                  alt="LinkedIn Logo"
                />
              </a>
            )}
          </div>
        </form>
        <div className={styles.errorMessages}>
          <div>{errorMessages.empty && errorMessages.empty}</div>
          <div>{errorMessages.invalid && errorMessages.invalid}</div>
        </div>
      </div>
    </Container>
  );
}
