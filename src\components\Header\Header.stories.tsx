import Header from './Header';

export default {
  title: 'Components/Header',
};

const data = {
  data: {
    id: 1,
    attributes: {
      createdAt: '2024-05-20T04:52:06.170Z',
      updatedAt: '2024-05-31T10:05:13.465Z',
      publishedAt: '2024-05-29T09:35:34.042Z',
      logo: {
        id: 1,
        link: '/',
        image: {
          data: {
            id: 10,
            attributes: {
              name: 'marutitech_logo.svg',
              alternativeText: 'mtechlogo.svg',
              caption: 'mtechlogo.svg',
              width: 49,
              height: 23,
              formats: null,
              hash: 'marutitech_logo_fe5e4e6162',
              ext: '.svg',
              mime: 'image/svg+xml',
              size: 3.13,
              url: 'https://cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg',
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-05-22T13:48:47.033Z',
              updatedAt: '2024-05-29T09:55:16.387Z',
            },
          },
        },
      },
      menu: [
        {
          id: 3,
          __component: 'header.menu-1',
          title: 'Services',
          link: 'abc',
          button: {
            id: 2,
            title: 'All Services',
            link: 'abc',
          },
          subMenu: [
            {
              id: 8,
              title: 'Product Engineering',
              link: 'abc',
              sublinks: [
                {
                  title: 'Software Product Engineering',
                  link: 'abc',
                },
                {
                  title: 'Quality Engineering',
                  link: 'abc',
                },
                {
                  title: 'Maintenance & Support',
                  link: 'abc',
                },
              ],
            },
            {
              id: 9,
              title: 'DevOps & Cloud Engineering',
              link: 'abc',
              sublinks: [
                {
                  title: 'Cloud Application Development',
                  link: 'abc',
                },
                {
                  title: 'DevOps',
                  link: 'abc',
                },
              ],
            },
            {
              id: 10,
              title: 'Data, Analytics & AI',
              link: 'abc',
              sublinks: [
                {
                  title: 'Analytics',
                  link: 'abc',
                },
                {
                  title: 'Artificial Intelligence',
                  link: 'abc',
                },
              ],
            },
            {
              id: 11,
              title: 'Customer Experience',
              link: 'abc',
              sublinks: [
                {
                  title: 'Product Design & Strategy',
                  link: 'abc',
                },
                {
                  title: 'Interactive Experience',
                  link: 'abc',
                },
              ],
            },
            {
              id: 12,
              title: 'Digital & Technology Consulting',
              link: 'abc',
              sublinks: [
                {
                  title: 'Technology Advisory',
                  link: '/abc',
                },
              ],
            },
            {
              id: 13,
              title: 'IT Outsourcing',
              link: 'abc',
              sublinks: [
                {
                  title: 'Talent Augmentation',
                  link: '/abc',
                },
              ],
            },
          ],
        },
        {
          id: 4,
          __component: 'header.menu-2',
          title: 'Industries',
          link: 'abc',
          subLinks: [
            {
              id: 92,
              title: 'Healthcare',
              link: 'abc',
            },
            {
              id: 93,
              title: 'LegalTech',
              link: 'abc',
            },
            {
              id: 94,
              title: 'Insurance',
              link: 'abc',
            },
          ],
        },
        {
          id: 3,
          __component: 'header.menu-3',
          title: 'Insights',
          link: 'abc',
          titleDescription: {
            id: 3,
            title: 'Latest blog',
            description:
              '<p>AI for Lawyers: Can AI Robots&nbsp;<br>Defend a Human in Court?</p>',
          },
          button: {
            id: 5,
            title: 'See more',
            link: 'abc',
          },
          subLinks: [
            {
              id: 95,
              title: 'Blogs',
              link: 'abc',
            },
            {
              id: 96,
              title: 'Ebooks',
              link: 'abc',
            },
            {
              id: 97,
              title: 'Podcasts',
              link: 'abc',
            },
            {
              id: 98,
              title: 'Case Studies',
              link: 'abc',
            },
            {
              id: 99,
              title: 'Partners',
              link: 'abc',
            },
            {
              id: 100,
              title: 'Events & Webinars',
              link: 'abc',
            },
            {
              id: 101,
              title: 'Videos',
              link: 'abc',
            },
          ],
        },
        {
          id: 5,
          __component: 'header.menu-2',
          title: 'Careers',
          link: 'abc',
          subLinks: [
            {
              id: 102,
              title: 'Current Opportunities',
              link: 'abc',
            },
            {
              id: 106,
              title: 'Employee Testimonials',
              link: 'abc',
            },
            {
              id: 103,
              title: 'Values',
              link: 'abc',
            },
            {
              id: 104,
              title: 'Benefits',
              link: 'abc',
            },
            {
              id: 105,
              title: 'Life at MTL',
              link: 'abc',
            },
          ],
        },
        {
          id: 1,
          __component: 'header.menu-4',
          title: 'About Us',
          link: 'abc',
          button: {
            id: 4,
            title: 'Our Brands',
            link: 'abc',
          },
          subLinks: [
            {
              id: 107,
              title: 'Our Story',
              link: 'abc',
            },
            {
              id: 108,
              title: 'How We Work',
              link: 'abc',
            },
            {
              id: 109,
              title: 'Leadership Team',
              link: 'abc',
            },
            {
              id: 110,
              title: 'In News',
              link: 'abc',
            },
          ],
        },
      ],
    },
  },
};

export function HeaderStory() {
  return (
    <div>
      <Header headerData={data} />
    </div>
  );
}
