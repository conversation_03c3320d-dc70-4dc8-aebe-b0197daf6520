@value variables: "@styles/variables.module.css";
@value colorBlack, gray, colorWhite, gray300, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, fontWeight400, bodyTextMedium, bodyTextXSmall, bodyHeadingXS from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm-450, breakpoint-xl, breakpoint-md-769, breakpoint-sm-424, breakpoint-sm-427, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1366, breakpoint-sm from breakpoints;

.ourServiceSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 0;
  gap: 40px;
}

.ourServiceContainer {
  display: flex;
  justify-content: center;
}

.title_description_container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ourServiceTitle>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;
  color: colorBlack;
  user-select: none;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.ourServiceSubtitle {
  color: colorBlack;
  font-size: bodyTextMedium;
  font-weight: fontWeight400;
  text-align: center;
  padding: 0 12px;
  user-select: none;

  @media (max-width: breakpoint-sm-450) {
    font-size: bodyTextXSmall;
    display: inline-block;
    padding: 0 16px;
  }
}

.cardContainer {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  transition:
    width 0.8s ease-in-out,
    height 0.8s ease-in-out;

  @media (max-width: breakpoint-xl-1024) {
    width: 680px !important;
  }

  @media (min-width: breakpoint-sm-424) and (max-width: 700px) {
    width: 400px !important;
  }

  @media (max-width: breakpoint-sm-427) {
    width: 300px !important;
  }
}

.cardCol {
  display: flex !important;
  flex-direction: column;
}

.row>* {
  @media (min-width: breakpoint-sm-320) and (max-width: breakpoint-sm-427) {
    padding-left: 0;
    padding-right: 0;
  }
}

.backdrop {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  transition: background-color 0.5s ease-in-out;

  @media screen and (max-width: breakpoint-xl-1024) {
    background: rgba(0, 0, 0, 0.7);
  }
}

.cardBody {
  color: colorWhite;
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 100%;
  transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  opacity: 0;
  visibility: hidden;
  width: 307px;
  transition:
    opacity 0.8s ease-in-out,
    visibility 0.8s ease-in-out;

  @media screen and (min-width: 1025px) and (max-width: 1222px) {
    max-width: 252px;
  }

  @media (min-width: breakpoint-sm-424) and (max-width: 700px) {
    width: 299px !important;
  }

  @media (max-width: breakpoint-sm-424) {
    width: 250px;
  }

  @media screen and (min-width: 701px) and (max-width: 1024px) {
    width: 628px;
  }
}

.cardTitle>h3 {
  font-weight: 600;
  font-size: 32px;
  line-height: 42.24px;
  padding-bottom: 8px;
}

.cardBody.visible {
  opacity: 1;
  left: 5%;
  visibility: visible;
}

.cardDescription {
  padding-top: 8px;
}

.cardTag {
  display: inline-block;
  padding-top: 8px;
  font-size: bodyHeadingXS;
  color: colorBlack;
}

.link {
  text-decoration: none;
  display: table-caption;

  @media (max-width: breakpoint-xl-1366) {
    display: block;
    margin: 0 auto;
  }
}

.serviceImage {
  width: 100%;
  height: 100%;
  object-fit: cover;

  @media (max-width: breakpoint-xl-1366) {
    width: 100%;
  }
}

.cardContainer:hover .serviceImage {
  transform: scale(1.05);
}

.serviceRow {
  display: flex;
  gap: 20px;
  flex-wrap: nowrap;
  max-width: 1192px;

  @media screen and (min-width: 1025px) and (max-width: 1222px) {
    max-width: 900px;
  }

  @media (max-width: breakpoint-xl-1024) {
    justify-content: center;
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 30px;
  }
}

/* variant blackSlideCard styles */

.blackSlideCardWrapper {
  padding: 80px 0px;
  user-select: none;
}

.whiteSlideCardWrapper {
  background-color: gray300;
}

.blackSlideContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.blackSlideTitle>h2 {
  color: colorBlack;
  margin-bottom: 40px;
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;
  letter-spacing: 0.8px;
  user-select: none;

  @media screen and (max-width: breakpoint-sm) {
    margin-bottom: 30px;
    font-size: 28px;
    line-height: 38.64px;
    letter-spacing: 0.84px;
  }
}

.blackSlideCard {
  min-height: 378px;
  width: 283px;
  border: 0;
  border-radius: 6px;
  background-color: colorBlack;
  position: relative;
  color: white;
  overflow: hidden;
  box-shadow:
    -5px 35px 130px -90px #ae1f5d60,
    -5px 35px 130px -90px #d31a5e60,
    -5px 35px 130px -90px #ed7a3760,
    -5px 35px 130px -90px #f8b81060;

  @media (max-width: breakpoint-xl-1024) {
    transform: translateY(0px);
    bottom: 0;
  }
}

.whiteSlideCard {
  background-color: colorWhite;
}

.servicePageLink {
  text-decoration: none;
}

.blackSlideCardTitle>h3 {
  font-size: 20px;
  color: white;
  padding: 24px;
  font-weight: 600;
  z-index: 2;
  user-select: none;
}

.whiteSlideCardTitle>h3 {
  font-size: 20px;
  color: colorBlack;
  padding: 24px;
  font-weight: 600;
  z-index: 2;
  user-select: none;
}

.blackSlideCardDescription {
  position: absolute;
  top: 175px;
  left: 28px;
  right: 28px;
  font-size: 14px;
  transition: transform 0.3s ease-in-out;
  z-index: 2;
  user-select: none;
  display: -webkit-box;
  -webkit-line-clamp: 9;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: breakpoint-xl-1024) {
    transform: translateY(-50px);
    bottom: 0;
  }
}

.blackSlideCard:hover .blackSlideCardDescription {
  transform: translateY(-50px);
}

.blackSlideCard:hover .arrow_button {
  transform: translateY(0%);
  bottom: 15px;
}

.whiteSlideCardDescription {
  position: absolute;
  color: colorBlack;
  top: 175px;
  left: 28px;
  right: 28px;
  font-size: 14px;
  transition: transform 0.3s ease-in-out;
  z-index: 2;
  user-select: none;
  display: -webkit-box;
  -webkit-line-clamp: 9;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: breakpoint-xl-1024) {
    transform: translateY(-50px);
    bottom: 0;
  }
}

.blackSlideCard:hover .whiteSlideCardDescription {
  transform: translateY(-50px);
}

.arrow_button {
  z-index: 3;
  transform: translateY(145%);
  position: absolute;
  left: 28px;
  bottom: 0;
  transition:
    transform 0.4s ease-in-out,
    bottom 0.4s ease-in-out;

  @media (max-width: breakpoint-xl-1024) {
    transform: translateY(0%);
    bottom: 15px;
  }
}

.gradientImage {
  position: absolute;
  left: -100%;
  top: 20%;
  width: 100%;
  height: 100%;
  transition:
    transform 0.6s ease-in-out,
    opacity 0.6s ease-in-out,
    left 0.6s ease-in-out;
  transform-origin: left center;
  transform: rotate(-15deg);
  z-index: 1;

  @media (max-width: breakpoint-xl-1024) {
    transform: rotate(0deg);
    left: 0px;
  }
}

.blackSlideCard:hover .gradientImage {
  transform: rotate(0deg);
  left: 0px;
}

.embla {
  max-width: 83%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 20px;
  --slide-size: auto;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.withoutCarouselContainer {
  display: flex;
  justify-content: center;
  gap: 30px;

  .blackSlideCard {
    width: 283px;
  }

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__controls {
  display: grid;
  grid-template-columns: none;
  justify-content: center;
  /* gap: 1.2rem; */
  margin-top: 40px;
  padding-left: 0;
  /* @media (max-width: breakpoint-md-769) {
    justify-content: center;
    grid-template-columns: none;
    padding-left: 0;
  } */
}

.embla__dots {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
  outline: none;
  border: 0;
}

.embla__dot {
  background: rgba(217, 217, 217, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  width: 11px;
  height: 11px;
  margin: 0 3.5px;
  border: 0;
  border-radius: 50%;
  outline: none;
  transition: 0.5s width;
}

.embla__dot--selected {
  width: 26px;
  height: 12px;
  border: 0;
  outline: none;
  border-radius: 20px;
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
}

.centerAlign .embla__container {
  justify-content: center;
}

.headingContainer {
  display: flex;
  justify-content: space-between;
  padding: 0 12rem;

  @media screen and (max-width: 1500px) {
    padding: 0 6.5rem;
  }

  @media screen and (max-width: 1200px) {
    padding: 0 4rem;

  }


}

.carouselArrowButtons {
  display: flex;

  @media (max-width: breakpoint-md-769) {
    display: none;
  }
}

.embla__controls_whiteslide {
  display: grid;
  grid-template-columns: none;
  justify-content: center;
  margin-top: 40px;
  padding-left: 0;
  display: none;

  @media screen and (max-width: breakpoint-md-769) {
    display: grid;

  }
}