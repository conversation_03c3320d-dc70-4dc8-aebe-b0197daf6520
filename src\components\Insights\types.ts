export interface InsightsSliderTypes {
  id: number;
  attributes?: any;
}

export interface InsightsTypes {
  data: {
    id: number;
    subtitle: string;
    taglineUrl: string;
    title: string;
    circular_text_image: {
      data: {
        id: number;
        attributes: {
          alternativeText: string;
          caption: string;
          createdAt: string;
          ext: string;
          formats: null;
          hash: string;
          height: number;
          mime: string;
          name: string;
          previewUrl: null;
          provider: string;
          provider_metadata: null;
          size: number;
          updatedAt: string;
          url: string;
          width: number;
        };
      };
    };
    blogs?: any;
  };
  variantWhite?: boolean;
}
