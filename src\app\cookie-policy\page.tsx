import RichText from '@components/RichText';
import RichResults from '@components/RichResults';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';

async function fetchCookiePolicyPageData() {
  const queryString = 'populate=rich_text,seo.schema';
  return await fetchFromStrapi('cookie-policy', queryString);
}

export async function generateMetadata({}) {
  const queryString =
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema';
  const seoFetchedData = await fetchFromStrapi('cookie-policy', queryString);
  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}
export default async function CookiePolicy() {
  const cookiePolicyPageData = await fetchCookiePolicyPageData();
  return (
    <>
      {cookiePolicyPageData?.data?.attributes?.seo && (
        <RichResults data={cookiePolicyPageData?.data?.attributes?.seo} />
      )}
      {cookiePolicyPageData?.data?.attributes?.rich_text && (
        <RichText richTextData={cookiePolicyPageData?.data?.attributes} />
      )}
    </>
  );
}
