@value variables: "@styles/variables.module.css";
@value colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-md, breakpoint-sm from breakpoints;

.main_container {
  position: relative;
  overflow: hidden;
  background-color: #000000d9;
  min-height: 514px;
  padding: 5rem 9.375rem;
  height: fit-content;
  display: flex;
  align-items: flex-end;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 5rem 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 5rem 1rem;
  }
}

.background_image {
  object-fit: cover;
  z-index: -1;
}

.title > h1 {
  max-width: 1166px;
  color: colorWhite;
  font-size: 52px;
  font-weight: 600;
  line-height: 120%;
  letter-spacing: -1.04px;

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 138%;
    letter-spacing: -0.84px;
  }
}
