@value variables: "@styles/variables.module.css";
@value gray500, gray900, colorWhite, brandColorThree from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1309, breakpoint-sm-450, breakpoint-sm, breakpoint-xl-2560, breakpoint-xl, breakpoint-sm-450, breakpoint-lg from breakpoints;

.container {
  position: sticky;
  top: 90px;
  left: 0;
  right: 0;
  margin-top: 80px;
}

@media screen and (max-width: breakpoint-lg) {
  .container {
    display: none;
  }
}

.container_div {
  max-height: 462px;
  overflow-y: scroll;
}

@media screen and (min-width: 1900px) {
  .container_div {
    width: 450px;
  }
}

.toc_title {
  margin-bottom: 20px;
  color: gray900;

  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
}

.scroll_item {
  height: 66px;
  border-bottom: 2px solid gray500;
  display: flex;
  align-items: center;
  color: gray900;
}

.scroll_item:first-child {
  border-top: 2px solid gray500;
}

.scroll_item:hover {
  border-bottom: 2px solid brandColorThree;
  color: brandColorThree !important;
  cursor: pointer;
}

.title {
  font-size: 16px;
  font-weight: 400;
  font-style: normal;
  line-height: 144%;
}

@media screen and (min-width: breakpoint-xl-2560) {
  .title {
    font-size: 18px;
  }
}

@media screen and (max-width: breakpoint-xl) {
  .title {
    font-size: 14px;
  }
}

.active {
  border-bottom: 2px solid brandColorThree;
}

.active div {
  color: brandColorThree !important;
  font-weight: 700;
}

.mobile_view_TOC {
  display: flex;
  justify-content: center;
  align-items: center;
  color: brandColorThree;
  margin: auto;
}

@media screen and (min-width: breakpoint-lg) {
  .mobile_view_TOC {
    display: none;
  }
}

.dropdown_menu {
  margin-top: -13px !important;
  z-index: 2;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: clamp(305px, 84.8vw, 800px);
  margin: auto;
  flex-direction: column;
  background: colorWhite;
  padding-bottom: 20px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  max-height: 470px;
  overflow-y: scroll;
}

@media screen and (min-width: breakpoint-lg) {
  .dropdown_menu {
    display: none;
  }
}

@media screen and (max-width: breakpoint-sm) {
  .dropdown_menu {
    width: clamp(300px, 81vw, 470px);
  }
}

@media screen and (max-width: breakpoint-sm-450) {
  .dropdown_menu {
    width: clamp(200px, 76vw, 350px);
  }
}

.dropdown_menu::-webkit-scrollbar {
  background-color: colorWhite;
  width: 14px;
}

.dropdown_menu::-webkit-scrollbar-track {
  background-color: colorWhite;
}

.dropdown_menu::-webkit-scrollbar-thumb {
  background-color: gray500;
  border-radius: 16px;
  border: 4px solid colorWhite;
}

.dropdown_menu_div {
  width: 90%;
  height: 68px;
  border-bottom: 2px solid gray500;
  display: flex;
  align-items: center;
  margin: auto;
}

.dropdown_data {
  font-size: 16px;
  line-height: 19.68px;
  color: gray900;
  font-weight: 400;
}

.mobile_active {
  border-bottom: 2px solid brandColorThree;
}

.mobile_active div {
  color: brandColorThree !important;
}

.Hide_dropdown_menu {
  display: none !important;
}