import { useCallback, useEffect, useRef, useState } from 'react';

const useMediaQueryState =
  typeof window === 'undefined' || typeof window.matchMedia === 'undefined'
    ? () => false
    : ({ query }: { query: string }) => {
        const [isMatch, setIsMatch] = useState(false);
        const mediaQueryRef = useRef(window.matchMedia(query));

        const handleMatch = useCallback(
          event => {
            setIsMatch(event.matches);
          },
          [setIsMatch],
        );

        useEffect(() => {
          const mediaQueryList = mediaQueryRef.current;
          setIsMatch(mediaQueryList.matches);
          mediaQueryList.addListener(handleMatch);

          return () => mediaQueryList.removeListener(handleMatch);
        }, [setIsMatch, handleMatch]);

        return isMatch;
      };

export default useMediaQueryState;
