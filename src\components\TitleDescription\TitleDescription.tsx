'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Heading from '@components/Heading';

import styles from './TitleDescription.module.css';
import Image from 'next/image';
import ImageWithSizing from '@components/ImageWithSizing';

export default function TitleDescription({
  dataTitleDescription,
  variant = 'default',
}) {
  return (
    <>
      {variant === 'default' && (
        <Container fluid className={styles.container}>
          <Heading
            headingType="h2"
            title={dataTitleDescription?.title}
            className={styles.title}
          />
          <div
            className={styles.description}
            dangerouslySetInnerHTML={{
              __html: dataTitleDescription?.description,
            }}
          ></div>
        </Container>
      )}
      {variant === 'with_image' && (
        <Container fluid className={styles.container}>
          <Heading
            headingType="h2"
            title={dataTitleDescription?.title}
            className={styles.title}
          />
          <ImageWithSizing
            src={dataTitleDescription?.image?.data?.attributes}
            alt={dataTitleDescription?.image?.data?.attributes?.alternativeText}
            width={1192}
            height={617}
            className={styles.image}
          />
        </Container>
      )}
      {variant === 'employee' && (
        <Container fluid className={styles.employee_container}>
          <Heading
            headingType="h2"
            title={dataTitleDescription?.title}
            className={styles.title}
          />
          <div className={styles.employeeGrid}>
            {dataTitleDescription?.emp_details.map((item, index) => (
              <div key={index} className={styles.img_text_container}>
                <ImageWithSizing
                  src={item?.image?.data?.attributes}
                  alt={item?.image?.data?.attributes?.alternativeText}
                  width={295}
                  height={189}
                  className={styles.employeeImage}
                />
                <div className={styles.employeeTextContainer}>
                  <Heading
                    headingType="h6"
                    title={item?.title}
                    className={styles.employeeTitle}
                  />
                  <div
                    className={styles.employeeDescription}
                    dangerouslySetInnerHTML={{
                      __html: item?.description,
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </Container>
      )}
    </>
  );
}
