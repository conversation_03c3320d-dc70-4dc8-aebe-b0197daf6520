'use client';

import useEmblaCarousel from 'embla-carousel-react';
import React, { useState, useRef } from 'react';
import { Container } from 'react-bootstrap';
import useDotButton from '@hooks/useDotButton';
import styles from './TabChallenges.module.css';
import Heading from '@components/Heading';
import Image from 'next/image';
import ServicesCard from '@components/ServicesCard';
import CTA from '@components/CTA';
import TrustedPartners from '@components/TrustedPartners';
import CaseStudyCard from '@components/CaseStudyCard';
import WhyChooseMTL from '@components/WhyChooseMTL';
import TechStack from '@components/TechStack';
import AwardsRecognition from '@components/AwardsRecognition';
import ClutchReviews from '@components/ClutchReviews';
import Insights from '@components/Insights';
import Faq from '@components/Faq';
import ContactUsForm from '@components/ContactUsForm';
import DotButton from '@components/DotButton/DotButton';

import emblastyles from '../../styles/emlaDots.module.css';
import HeroSection from '@components/HeroSection';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '../../styles/breakpoints.module.css';
import Deliverables from '@components/Deliverables';

export default function TabChallenges({
  variant,
  tabChallengesData = null,
  awardsData = null,
  formData = null,
  trustedPartnersData = null,
}: {
  variant: string;
  tabChallengesData?: any;
  awardsData?: any;
  formData?: any;
  trustedPartnersData?: any;
}) {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    dragFree: true,
  });
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);
  const showSlider = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1365']})`,
  });
  const rightContainerRef = useRef<HTMLDivElement | null>(null);

  return (
    <>
      {variant === 'industry' && (
        <Container fluid className={styles.main_container}>
          <Heading
            headingType="h2"
            title={tabChallengesData?.title}
            position="left"
            className={styles.main_title}
          />
          <div className={styles.inner_container}>
            <div className={styles.tab_boxes}>
              {tabChallengesData?.tab_box.map((tabData, index) => {
                return (
                  <div
                    className={
                      index === selectedTabIndex
                        ? styles.single_image_selected
                        : styles.single_image
                    }
                    key={tabData?.id}
                    onClick={() => {
                      setSelectedTabIndex(index);
                      if (
                        window.innerWidth <= 450 &&
                        rightContainerRef.current
                      ) {
                        rightContainerRef.current.scrollIntoView({
                          behavior: 'smooth',
                        });
                      }
                    }}
                  >
                    <Image
                      src={tabData?.card_image?.data?.attributes?.url}
                      width={182}
                      height={122}
                      alt={'background image'}
                      className={styles.imageWrapper}
                    />
                    {index !== selectedTabIndex && (
                      <div className={styles.overlay}></div>
                    )}
                    <div className={styles.titleWrapper}>
                      <div
                        className={
                          index === selectedTabIndex
                            ? styles.imageTitle_selected
                            : styles.imageTitle
                        }
                      >
                        {tabData?.card_title}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className={styles.right_container} ref={rightContainerRef}>
              <div className={styles.right_box}>
                <Heading
                  headingType="h3"
                  title={
                    tabChallengesData?.tab_box[selectedTabIndex]
                      ?.challenges_title
                  }
                  className={styles.right_title}
                />
                <div
                  className={styles.right_richText}
                  dangerouslySetInnerHTML={{
                    __html:
                      tabChallengesData?.tab_box[selectedTabIndex]
                        ?.challenges_description,
                  }}
                ></div>
                <Image
                  src={'https://cdn.marutitech.com/arrow_down_4a2eb1486f.svg'}
                  width={32}
                  height={50}
                  alt="arrow down"
                />
              </div>
              <div className={styles.right_box}>
                <Heading
                  headingType="h3"
                  title={
                    tabChallengesData?.tab_box[selectedTabIndex]?.solution_title
                  }
                  className={styles.right_title}
                />
                <div
                  className={styles.right_richText}
                  dangerouslySetInnerHTML={{
                    __html:
                      tabChallengesData?.tab_box[selectedTabIndex]
                        ?.solution_description,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </Container>
      )}
      {variant === 'retail' && (
        <>
          {tabChallengesData?.hero_section && (
            <HeroSection
              heroData={tabChallengesData?.hero_section}
              variant="primary"
              industrySlug={tabChallengesData?.slug}
              industryName={tabChallengesData?.pageName}
            />
          )}

          <div className={styles.tab_retail_section}>
            {tabChallengesData?.tab_section?.title && (
              <Heading
                headingType="h2"
                title={tabChallengesData?.tab_section?.title}
                className={styles.retail_title}
              />
            )}

            {tabChallengesData?.tab_section?.description && (
              <Heading
                headingType="h3"
                className={styles.retail_description}
                richTextValue={tabChallengesData?.tab_section?.description}
              />
            )}
            {showSlider ? (
              <section className={styles.embla}>
                <div className={styles.embla__viewport} ref={emblaRef}>
                  <div className={styles.embla__container}>
                    {tabChallengesData?.retail_components?.data.map(
                      (tabData, index) => (
                        <div className={styles.embla__slide} key={tabData?.id}>
                          <div
                            className={
                              index === selectedTabIndex
                                ? `${styles.tab_retail_box} ${styles.tab_retail_box_selected}`
                                : `${styles.tab_retail_box}`
                            }
                            onClick={() => setSelectedTabIndex(index)}
                          >
                            <div>{tabData?.attributes?.tab_title}</div>
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </div>
                <div
                  className={`${emblastyles.embla__controls} ${styles.embla__controls}`}
                >
                  <div className={emblastyles.embla__dots}>
                    {scrollSnaps.length > 1 &&
                      scrollSnaps.map((_, index) => (
                        <DotButton
                          key={index}
                          onClick={() => onDotButtonClick(index)}
                          className={
                            index === selectedIndex
                              ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                              : `${emblastyles.embla__dot} ${emblastyles.embla__dot_bg_white}`
                          }
                        />
                      ))}
                  </div>
                </div>
              </section>
            ) : (
              <section className={styles.retail_container}>
                {tabChallengesData?.retail_components?.data.map(
                  (tabData, index) => (
                    <div
                      className={
                        index === selectedTabIndex
                          ? `${styles.tab_retail_box} ${styles.tab_retail_box_selected}`
                          : `${styles.tab_retail_box}`
                      }
                      key={tabData?.id}
                      onClick={() => setSelectedTabIndex(index)}
                    >
                      <div>{tabData?.attributes?.tab_title}</div>
                    </div>
                  ),
                )}
              </section>
            )}
          </div>

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.challenges_and_solutions && (
            <Deliverables
              datadeliverables={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.challenges_and_solutions
              }
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.what_service_we_are_offering && (
            <ServicesCard
              variant="blackSlideCard"
              l2ServiceData={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.what_service_we_are_offering
              }
              background="black"
              variantWhite={false}
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.cta && (
            <CTA
              data={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.cta
              }
              variant="scrollToContactForm"
            />
          )}

          {trustedPartnersData && (
            <TrustedPartners data={trustedPartnersData} />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.case_study_cards && (
            <CaseStudyCard
              case_study={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.case_study_cards
              }
              variantWhite={true}
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.why_choose_maruti_techlabs && (
            <WhyChooseMTL
              data={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.why_choose_maruti_techlabs
              }
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.cta_other && (
            <CTA
              data={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.cta_other
              }
              variant="scrollToContactForm"
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.tech_stack && (
            <TechStack
              data={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.tech_stack
              }
            />
          )}

          {awardsData && (
            <AwardsRecognition awardsRecognitionData={awardsData} />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.clutch_reviews && (
            <ClutchReviews
              data={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.clutch_reviews
              }
              variantWhite={true}
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.insights && (
            <Insights
              data={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.insights
              }
            />
          )}

          {tabChallengesData.retail_components?.data[selectedTabIndex]
            ?.attributes?.faq && (
            <Faq
              faqData={
                tabChallengesData.retail_components?.data[selectedTabIndex]
                  ?.attributes?.faq
              }
            />
          )}

          {formData && <ContactUsForm formData={formData} source="Retail" />}
        </>
      )}
      {variant === 'cloud' && (
        <Container fluid className={styles.container_cloud}>
          <div className={styles.content}>
            <Heading
              className={styles.title_cloud}
              title={tabChallengesData.title}
              headingType="h2"
            />
            <div
              className={styles.description_cloud}
              dangerouslySetInnerHTML={{
                __html: tabChallengesData.description,
              }}
            />
          </div>
          <div className={styles.inner_container_cloud}>
            <div className={styles.tab_boxes_cloud}>
              {tabChallengesData?.box.map((tabData, index) => {
                return (
                  <div
                    className={
                      index === selectedTabIndex
                        ? styles.single_box_selected_cloud
                        : styles.single_box_cloud
                    }
                    key={tabData?.id}
                    onClick={() => setSelectedTabIndex(index)}
                  >
                    {tabData?.title}
                  </div>
                );
              })}
            </div>
            <div className={styles.right_container_cloud}>
              <div className={styles.right_box_cloud}>
                <div
                  className={styles.right_richText_cloud}
                  dangerouslySetInnerHTML={{
                    __html:
                      tabChallengesData?.box[selectedTabIndex]?.description,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </Container>
      )}
    </>
  );
}
