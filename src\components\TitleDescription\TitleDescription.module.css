@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, gray200 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm from breakpoints;

.container {
  padding: 80px 124px;
  background-color: colorBlack;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.employee_container {
  padding: 80px 8px;
  background-color: colorBlack;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px 8px;
  }
}

.title h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 138%;
    letter-spacing: -0.8px;
  }
}

.description {
  color: colorWhite;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
}


.employeeGrid {
  display: flex;
  max-width: 1197px;
  flex-wrap: wrap;
  gap: 5px;

  @media screen and (min-width: 990px) and (max-width: 1216px) {
    max-width: 947px;
    justify-content: center;
  }

  @media screen and (min-width: 768px) and (max-width: 990px) {
    max-width: 709px;
    justify-content: center;
  }

  @media screen and (max-width: 768px) {
    max-width: 471px;
    justify-content: center;
  }

  @media screen and (max-width: 576px) {
    max-width: 305px;
    justify-content: center;

  }

}

.employeeImage {
  @media screen and (max-width: 576px) {
    width: 150px;
    height: 120px;
  }
}

.img_text_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: fit-content;
}

.employeeTextContainer {
  display: flex;
  flex-direction: column;
  padding: 20px 0 0 20px;
  width: 295px;
  background-color: gray200;

  @media screen and (max-width: 576px) {
    padding: 5px;
    width: 150px;
  }
}

.employeeTitle>h6 {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  color: colorBlack;
}

.employeeDescription {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: colorBlack;

}