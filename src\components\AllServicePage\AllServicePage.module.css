@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md-767, breakpoint-xl-1800: 1800px, breakpoint-sm-450 from breakpoints;

.main_container {
  padding: 0;
}

.vision_container {
  display: flex;

  @media screen and (max-width: 768px) {
    flex-direction: column;
  }
}

.mission_container {
  display: flex;
  flex-direction: row-reverse;

  @media screen and (max-width: 768px) {
    flex-direction: column;
  }
}

.box_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px;
  gap: 16px;
  background-color: colorBlack;
}

.title > h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.richText {
  color: colorWhite;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.image_container {
  overflow: hidden;
}

.image_container,
.box_container {
  width: 50%;

  @media screen and (max-width: 768px) {
    width: 100%;
  }
}

.image {
  width: 100%;
  transition: transform 0.5s ease;

  @media screen and (min-width: breakpoint-xl-1800) {
    height: 889px;
  }

  @media screen and (min-width: 768px) and (max-width: 1100px) {
    height: 660px;
  }

  @media screen and (max-width: breakpoint-md-767) {
    height: 532px;
  }

  @media screen and (max-width: breakpoint-sm-450) {
    height: 320px;
  }
}

.image_container:hover .image {
  transform: scale(1.1);
}

.card__number {
  color: colorWhite;
  width: 32px;
  position: relative;
  padding: 2px 6px;
  text-align: center;

  font-size: 16px;
  font-weight: 400;
  font-style: normal;
  line-height: normal;
}

.card__number::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 3px;
  padding: 1.5px;
  background: linear-gradient(
    93.12deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.right_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.subheading {
  color: colorWhite;
  font-size: 20px;
  font-weight: 600;
  line-height: 144%;
}

.arrow_service_page {
  display: flex;
  gap: 6px;
  text-decoration: none;
}

.service_page_title {
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 144%;
}

.arrow_pages_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: fit-content;
}
