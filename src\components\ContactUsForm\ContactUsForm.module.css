@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-md-767, breakpoint-sm, breakpoint-xl-1024, breakpoint-xl, breakpoint-xl-1400 from breakpoints;

.ContactUsFormContainer {
  margin: 0;
  padding: 0;
  position: relative;
  color: colorWhite;
  background-color: colorBlack;
}

.backgroundImage {
  object-fit: scale-down;
  object-position: bottom right;
}

.formWrapper {
  position: relative;
  z-index: 10;
  padding: 5rem 8rem;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: breakpoint-xl-1024) {
    padding: 2.5rem 2rem;
  }
}

.formHeader {
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media (max-width: breakpoint-sm) {
    text-align: center;
  }
}

.formTitle h3 {
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media (max-width: breakpoint-md) {
    font-size: 32px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
    letter-spacing: -0.84px;
  }
}

.formInstructions {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;

  @media (max-width: breakpoint-md) {
    font-size: 15px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 14px;
  }
}

.form {
  width: 66.66666666666666666%;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: breakpoint-md) {
    width: 100%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 85%;
  }

  @media (min-width: breakpoint-xl-1400) {
    width: 793px;
  }
}

.formFields {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.personalDetailsWrapper {
  display: flex;
  gap: 24px;
  flex-direction: column;

  @media (min-width: breakpoint-sm) and (max-width: breakpoint-md) {
    width: 66.6%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 66.6%;
  }
}

.row {
  display: flex;
  gap: 20px;
}

.nameAndInputWrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.width25 {
  width: 23.5%;
}

.width47andHalf {
  width: 47.5%;
}

.width48andHalf {
  width: 48.5%;
}

.width100 {
  width: 100%;
}

.firstRow {
  flex-direction: row;

  @media (max-width: breakpoint-xl-1024) {
    flex-direction: column !important;
    gap: 24px;
  }
}

.nameFields {
  @media (max-width: breakpoint-md) {
    width: 100%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.emailIdField {
  @media (max-width: breakpoint-md) {
    width: 100%;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.secondRow {
  flex-direction: row;

  @media (max-width: breakpoint-md) {
    flex-direction: column;
    gap: 24px;
  }

  @media (min-width: breakpoint-md) and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 24px;
  }
}

.companyNameWrapper {
  @media (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.phoneNumberWrapper {
  @media (max-width: breakpoint-xl-1024) {
    width: 100%;
  }
}

.formLabel {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;

  @media (max-width: breakpoint-md) {
    font-size: 15px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 14px;
  }
}

.formInput {
  background: gray;
  color: colorWhite;
  border-radius: 3px;
  height: 44px;
  border: none;
  padding: 12px;
  font-size: 16px;
  line-height: 1.5;

  @media (max-width: breakpoint-md) {
    font-size: 15px;
    height: 42px;
    padding: 11px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 14px;
    height: 40px;
    padding: 10px;
  }
}

.formInputPhone {
  background: gray !important;
  color: colorWhite !important;
  height: 44px !important;
  width: 100% !important;
  border: none !important;
  padding: 12px;
  box-shadow: none !important;
  font-size: 16px !important;

  @media (max-width: breakpoint-md) {
    font-size: 15px !important;
    height: 42px !important;
    padding: 11px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 14px !important;
    height: 40px !important;
    padding: 10px;
  }
}

.formInputPhone_dial_icon {
  background: gray !important;
  color: colorWhite !important;
  height: 44px !important;
  padding: 12px;
  border: none !important;
  box-shadow: none !important;

  @media (max-width: breakpoint-md) {
    height: 42px !important;
    padding: 11px;
  }

  @media (max-width: breakpoint-sm) {
    height: 40px !important;
    padding: 10px;
  }
}

.ph_number_countries_dropdown {
  color: colorBlack !important;
}

.formInputForHowCanWeHelpYou {
  height: 82px;
  max-height: 250px;
}

.formInput:focus-visible {
  border: 0;
  margin: 0;
}

.consentRow {
  gap: 12px;
  justify-content: normal;
}

.consentText {
  user-select: none;
  cursor: pointer;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.4;

  @media (max-width: breakpoint-md) {
    font-size: 14px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 13px;
  }
}

.submitButtonRow {
  align-items: center;
  justify-content: start;
  gap: 24px;

  @media (max-width: breakpoint-md-767) {
    flex-direction: column;
    gap: 24px;
  }
}

.submitButton {
  padding: 16px 36px !important;

  @media (max-width: breakpoint-md-767) {
    width: 100%;
  }
}

.submitButton>div {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;

  @media (max-width: breakpoint-md) {
    font-size: 16px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 15px;
  }
}

.submitButton::before {
  border-radius: 6px;
  padding: 2px;
}

.linkedInButton {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.4;
  text-decoration: none;

  @media (max-width: breakpoint-md) {
    font-size: 14px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 13px;
  }
}

.errorInput {
  border: 1px solid #ff0000 !important;
}

.errorMessages {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  color: #ff0000;

  @media (max-width: breakpoint-md) {
    font-size: 13px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 12px;
  }
}

.errorLabel {
  color: #ff0000;
}

.container_spinner {
  position: relative;
  width: 170px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  background-image: linear-gradient(colorBlack, colorBlack),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.spinner {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(93deg,
      #febe10 0%,
      #f47a37 30.56%,
      #f05443 53.47%,
      #d91a5f 75.75%,
      #b41f5e 100%);

  -webkit-mask-image: radial-gradient(circle,
      rgba(0, 0, 0, 0) 55%,
      rgba(0, 0, 0, 1) 60%);
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}