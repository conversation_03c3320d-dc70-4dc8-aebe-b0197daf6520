import ClutchReviews from '.';

export default {
  title: 'Components/ClutchReviews',
};

const data = {
  id: 1,
  title: 'Clutch Reviews',
  review_image: {
    data: [
      {
        id: 140,
        attributes: {
          name: 'Review image.svg',
          alternativeText: null,
          caption: null,
          width: 350,
          height: 440,
          formats: null,
          hash: 'Review_image_967d2a63f0',
          ext: '.svg',
          mime: 'image/svg+xml',
          size: 107.74,
          url: 'https://cdn.marutitech.com/Review_image_967d2a63f0.svg',
          previewUrl: null,
          provider:
            '@strapi-community/strapi-provider-upload-google-cloud-storage',
          provider_metadata: null,
          createdAt: '2024-08-23T10:10:20.297Z',
          updatedAt: '2024-08-23T10:10:20.297Z',
        },
      },
      {
        id: 139,
        attributes: {
          name: 'Review image.svg',
          alternativeText: null,
          caption: null,
          width: 350,
          height: 440,
          formats: null,
          hash: 'Review_image_1cb360ffa7',
          ext: '.svg',
          mime: 'image/svg+xml',
          size: 107.74,
          url: 'https://cdn.marutitech.com/Review_image_1cb360ffa7.svg',
          previewUrl: null,
          provider:
            '@strapi-community/strapi-provider-upload-google-cloud-storage',
          provider_metadata: null,
          createdAt: '2024-08-23T10:10:08.455Z',
          updatedAt: '2024-08-23T10:10:08.455Z',
        },
      },
      {
        id: 138,
        attributes: {
          name: 'Review image.svg',
          alternativeText: null,
          caption: null,
          width: 350,
          height: 440,
          formats: null,
          hash: 'Review_image_22735545e9',
          ext: '.svg',
          mime: 'image/svg+xml',
          size: 107.74,
          url: 'https://cdn.marutitech.com/Review_image_22735545e9.svg',
          previewUrl: null,
          provider:
            '@strapi-community/strapi-provider-upload-google-cloud-storage',
          provider_metadata: null,
          createdAt: '2024-08-23T10:09:23.422Z',
          updatedAt: '2024-08-23T10:09:23.422Z',
        },
      },
    ],
  },
};

export function ClutchReviewsStory() {
  return (
    <div>
      <ClutchReviews data={data} />
    </div>
  );
}
