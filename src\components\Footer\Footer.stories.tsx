import Footer from './Footer';

export default {
  title: 'Components/Footer',
};
const data = {
    data: {
        id: 1,
        attributes: {
          createdAt: "2024-05-15T10:41:22.189Z",
          updatedAt: "2024-05-30T19:03:03.195Z",
          publishedAt: "2024-05-29T11:52:51.709Z",
          sector_row: [
            {
              id: 1,
              title: "Insight",
              link: "demo",
              Sublinks: [
                {
                  id: 22,
                  title: "Blogs",
                  link: "demo"
                },
                {
                  id: 23,
                  title: "Case Studies",
                  link: "demo"
                },
                {
                  id: 24,
                  title: "Videos",
                  link: "demo"
                },
                {
                  id: 25,
                  title: "Podcast",
                  link: "demo"
                },
                {
                  id: 26,
                  title: "White Paper",
                  link: "demo"
                },
                {
                  id: 27,
                  title: "Ebooks",
                  link: "demo"
                },
                {
                  id: 28,
                  title: "Partners",
                  link: "demo"
                },
                {
                  id: 29,
                  title: "Events & Webinars",
                  link: "demo"
                }
              ]
            },
            {
              id: 2,
              title: "Company",
              link: "demo",
              Sublinks: [
                {
                  id: 30,
                  title: "About Us",
                  link: "abc"
                },
                {
                  id: 31,
                  title: "How We Work",
                  link: "demo"
                },
                {
                  id: 32,
                  title: "Leadership Team",
                  link: "demo"
                },
                {
                  id: 33,
                  title: "Careers",
                  link: "demo"
                },
                {
                  id: 34,
                  title: "Awards & Recognition",
                  link: "demo"
                },
                {
                  id: 35,
                  title: "Contact Us",
                  link: "demo"
                }
              ]
            },
            {
              id: 3,
              title: "Careers",
              link: "abc",
              Sublinks: [
                {
                  id: 36,
                  title: "Values",
                  link: "abc"
                },
                {
                  id: 37,
                  title: "Values",
                  link: "abc"
                },
                {
                  id: 38,
                  title: "Life at MTL",
                  link: "demo"
                },
                {
                  id: 39,
                  title: "Current Opportunities",
                  link: "demo"
                },
                {
                  id: 40,
                  title: "Employee Testimonials",
                  link: "demo"
                }
              ]
            },
            {
              id: 4,
              title: "Industries",
              link: "demo",
              Sublinks: [
                {
                  id: 41,
                  title: "Insurance",
                  link: "demo"
                },
                {
                  id: 42,
                  title: "LegalTech",
                  link: "demo"
                },
                {
                  id: 43,
                  title: "Healthcare",
                  link: "demo"
                }
              ]
            }
          ],
          pages_row: [
            {
              id: 5,
              title: "Product Design & Strategy",
              link: "demo",
              Sublinks: [
                {
                  id: 44,
                  title: "UI/UX Design",
                  link: "demo"
                },
                {
                  id: 45,
                  title: "Rapid Prototyping",
                  link: "demo"
                },
                {
                  id: 46,
                  title: "Product Management",
                  link: "demo"
                },
                {
                  id: 47,
                  title: "User Research & Testing",
                  link: "demo"
                }
              ]
            },
            {
              id: 6,
              title: "Maintenance & Support",
              link: "demo",
              Sublinks: [
                {
                  id: 48,
                  title: "Application Support",
                  link: "demo"
                },
                {
                  id: 49,
                  title: "Infrastructure Support",
                  link: "demo"
                }
              ]
            },
            {
              id: 7,
              title: "Analytics",
              link: "demo",
              Sublinks: [
                {
                  id: 50,
                  title: "Data Engineering",
                  link: "demo"
                },
                {
                  id: 51,
                  title: "Business Intelligence",
                  link: "demo"
                }
              ]
            },
            {
              id: 8,
              title: "Interactive Experience",
              link: "demo",
              Sublinks: [
                {
                  id: 52,
                  title: "Chatbot Development",
                  link: "demo"
                },
                {
                  id: 53,
                  title: "Robotic Process Automation",
                  link: "demo"
                }
              ]
            },
            {
              id: 9,
              title: "Technology Advisory",
              link: "demo",
              Sublinks: [
                {
                  id: 54,
                  title: "Code Audit",
                  link: "demo"
                },
                {
                  id: 55,
                  title: "AWS Consulting",
                  link: "demo"
                },
                {
                  id: 56,
                  title: "GCP Consulting",
                  link: "demo"
                },
                {
                  id: 57,
                  title: "Business Technology Consulting",
                  link: "demo"
                },
                {
                  id: 58,
                  title: "Digital Transformation Consulting",
                  link: "demo"
                }
              ]
            },
            {
              id: 10,
              title: "Software Product Engineering",
              link: "demo",
              Sublinks: [
                {
                  id: 59,
                  title: "SaaS Development",
                  link: "demo"
                },
                {
                  id: 60,
                  title: "Web App Development",
                  link: "demo"
                },
                {
                  id: 61,
                  title: "Mobile App Development",
                  link: "demo"
                },
                {
                  id: 62,
                  title: "Low Code No Code Development",
                  link: "demo"
                }
              ]
            },
            {
              id: 11,
              title: "Quality Engineering",
              link: "demo",
              Sublinks: [
                {
                  id: 63,
                  title: "Security Testing",
                  link: "demo"
                },
                {
                  id: 64,
                  title: "Functional Testing",
                  link: "demo"
                },
                {
                  id: 65,
                  title: "Automation Testing",
                  link: "demo"
                },
                {
                  id: 66,
                  title: "Performance Testing",
                  link: "demo"
                }
              ]
            },
            {
              id: 12,
              title: "Artificial Intelligence",
              link: "demo",
              Sublinks: [
                {
                  id: 67,
                  title: "Computer Vision",
                  link: "demo"
                },
                {
                  id: 68,
                  title: "Machine Learning",
                  link: "demo"
                },
                {
                  id: 69,
                  title: "Natural Language Processing",
                  link: "demo"
                }
              ]
            },
            {
              id: 13,
              title: "Talent Augmentation",
              link: "demo",
              Sublinks: [
                {
                  id: 70,
                  title: "IT Outsourcing",
                  link: "demo"
                },
                {
                  id: 71,
                  title: "CTO as a Service",
                  link: "demo"
                },
                {
                  id: 72,
                  title: "Hire Node.js Developers",
                  link: "demo"
                },
                {
                  id: 73,
                  title: "On Demand Agile Teams",
                  link: "demo"
                },
                {
                  id: 74,
                  title: "Hire a Developer - Python",
                  link: "demo"
                },
                {
                  id: 75,
                  title: "Hire a Developer - Dot Net",
                  link: "demo"
                },
                {
                  id: 76,
                  title: "Hire a Developer - Angular",
                  link: "demo"
                },
                {
                  id: 77,
                  title: "Hire Mobile App Developers",
                  link: "demo"
                },
                {
                  id: 78,
                  title: "Dedicated Development Team",
                  link: "demo"
                }
              ]
            },
            {
              id: 14,
              title: "DevOps",
              link: "demo",
              Sublinks: [
                {
                  id: 79,
                  title: "Integration",
                  link: "demo"
                },
                {
                  id: 80,
                  title: "CI/CD Services",
                  link: "demo"
                },
                {
                  id: 81,
                  title: "Cloud Infrastructure Management",
                  link: "demo"
                },
                {
                  id: 82,
                  title: "Infrastructure as Code",
                  link: "demo"
                },
                {
                  id: 83,
                  title: "Containerization Services",
                  link: "demo"
                },
                {
                  id: 84,
                  title: "AWS Development Services",
                  link: "demo"
                },
                {
                  id: 85,
                  title: "GCP Development Services",
                  link: "demo"
                }
              ]
            },
            {
              id: 15,
              title: "Cloud Application Development",
              link: "demo",
              Sublinks: [
                {
                  id: 86,
                  title: "Cloud Consulting",
                  link: "demo"
                },
                {
                  id: 87,
                  title: "Cloud Security Services",
                  link: "demo"
                },
                {
                  id: 88,
                  title: "Serverless App Development",
                  link: "demo"
                },
                {
                  id: 89,
                  title: "Microservices Architecture  Development",
                  link: "demo"
                },
                {
                  id: 90,
                  title: "Cloud Migration Consulting",
                  link: "demo"
                },
                {
                  id: 91,
                  title: "Cloud Native App Development",
                  link: "demo"
                }
              ]
            }
          ],
          terms_and_condition_section: [
            {
              id: 1,
              title: "Terms of Use",
              link: "demo"
            },
            {
              id: 2,
              title: "Privacy Policy",
              link: "demo"
            },
            {
              id: 3,
              title: "Cookie Policy",
              link: "demo"
            },
            {
              id: 4,
              title: "Sitemap",
              link: "demo"
            }
          ],
          company_logo_section: {
            id: 1,
            link: "demo",
            Copyright: "©2024 Maruti TechLabs Pvt Ltd . All rights reserved. Privacy Policy",
            social_platforms: [
              {
                id: 1,
                link: "linkedin",
                image: {
                  data: {
                    id: 15,
                    attributes: {
                      name: "mdi_linkedin.svg",
                      alternativeText: null,
                      caption: null,
                      width: 41,
                      height: 40,
                      formats: null,
                      hash: "mdi_linkedin_898de0002b",
                      ext: ".svg",
                      mime: "image/svg+xml",
                      size: 1.23,
                      url: "https://cdn.marutitech.com/mdi_linkedin_898de0002b.svg",
                      previewUrl: null,
                      provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
                      provider_metadata: null,
                      createdAt: "2024-05-29T11:34:38.342Z",
                      updatedAt: "2024-05-29T11:34:38.342Z"
                    }
                  }
                }
              },
              {
                id: 2,
                link: "twitter",
                image: {
                  data: {
                    id: 15,
                    attributes: {
                      name: "mdi_linkedin.svg",
                      alternativeText: null,
                      caption: null,
                      width: 41,
                      height: 40,
                      formats: null,
                      hash: "mdi_linkedin_898de0002b",
                      ext: ".svg",
                      mime: "image/svg+xml",
                      size: 1.23,
                      url: "https://cdn.marutitech.com/mdi_linkedin_898de0002b.svg",
                      previewUrl: null,
                      provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
                      provider_metadata: null,
                      createdAt: "2024-05-29T11:34:38.342Z",
                      updatedAt: "2024-05-29T11:34:38.342Z"
                    }
                  }
                }
              },
              {
                id: 3,
                link: "facebook",
                image: {
                  data: {
                    id: 15,
                    attributes: {
                      name: "mdi_linkedin.svg",
                      alternativeText: null,
                      caption: null,
                      width: 41,
                      height: 40,
                      formats: null,
                      hash: "mdi_linkedin_898de0002b",
                      ext: ".svg",
                      mime: "image/svg+xml",
                      size: 1.23,
                      url: "https://cdn.marutitech.com/mdi_linkedin_898de0002b.svg",
                      previewUrl: null,
                      provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
                      provider_metadata: null,
                      createdAt: "2024-05-29T11:34:38.342Z",
                      updatedAt: "2024-05-29T11:34:38.342Z"
                    }
                  }
                }
              },
              {
                id: 4,
                link: "youtube",
                image: {
                  data: {
                    id: 15,
                    attributes: {
                      name: "mdi_linkedin.svg",
                      alternativeText: null,
                      caption: null,
                      width: 41,
                      height: 40,
                      formats: null,
                      hash: "mdi_linkedin_898de0002b",
                      ext: ".svg",
                      mime: "image/svg+xml",
                      size: 1.23,
                      url: "https://cdn.marutitech.com/mdi_linkedin_898de0002b.svg",
                      previewUrl: null,
                      provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
                      provider_metadata: null,
                      createdAt: "2024-05-29T11:34:38.342Z",
                      updatedAt: "2024-05-29T11:34:38.342Z"
                    }
                  }
                }
              }
            ],
            image: {
              data: {
                id: 14,
                attributes: {
                  name: "maruti_logo.png",
                  alternativeText: null,
                  caption: null,
                  width: 834,
                  height: 155,
                  formats: {
                    small: {
                      name: "small_maruti_logo.png",
                      hash: "small_maruti_logo_5897473ce8",
                      ext: ".png",
                      mime: "image/png",
                      path: null,
                      width: 500,
                      height: 93,
                      size: 17.99,
                      sizeInBytes: 17990,
                      url: "https://cdn.marutitech.com/small_maruti_logo_5897473ce8.png"
                    },
                    thumbnail: {
                      name: "thumbnail_maruti_logo.png",
                      hash: "thumbnail_maruti_logo_5897473ce8",
                      ext: ".png",
                      mime: "image/png",
                      path: null,
                      width: 245,
                      height: 46,
                      size: 7.85,
                      sizeInBytes: 7853,
                      url: "https://cdn.marutitech.com/thumbnail_maruti_logo_5897473ce8.png"
                    },
                    medium: {
                      name: "medium_maruti_logo.png",
                      hash: "medium_maruti_logo_5897473ce8",
                      ext: ".png",
                      mime: "image/png",
                      path: null,
                      width: 750,
                      height: 139,
                      size: 30.17,
                      sizeInBytes: 30168,
                      url: "https://cdn.marutitech.com/medium_maruti_logo_5897473ce8.png"
                    }
                  },
                  hash: "maruti_logo_5897473ce8",
                  ext: ".png",
                  mime: "image/png",
                  size: 5.46,
                  url: "https://cdn.marutitech.com/maruti_logo_5897473ce8.png",
                  previewUrl: null,
                  provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
                  provider_metadata: null,
                  createdAt: "2024-05-29T11:33:58.640Z",
                  updatedAt: "2024-05-29T11:33:58.640Z"
                }
              }
            }
          }
        }
      }
}
export function FooterStory() {
    return (
      <div>
        <Footer footerData={data}/>
      </div>
    );
  }
  