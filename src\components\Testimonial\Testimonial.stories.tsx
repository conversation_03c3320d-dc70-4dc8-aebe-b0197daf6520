import Testimonial from './Testimonial';

export default {
  title: 'Components/Testimonials',
};

const testimonialData = {
  id: 1,
  title: '<p>Success,<br>in their own words.</p>',
  tagline_url: '#',
  testimonial_playbtn_logo: {
    data: {
      id: 42,
      attributes: {
        name: 'Play Button.svg',
        alternativeText: null,
        caption: null,
        width: 49,
        height: 49,
        formats: null,
        hash: 'Play_Button_bd09bb5741',
        ext: '.svg',
        mime: 'image/svg+xml',
        size: 0.75,
        url: 'https://cdn.marutitech.com/Play_Button_bd09bb5741.svg',
        previewUrl: null,
        provider:
          '@strapi-community/strapi-provider-upload-google-cloud-storage',
        provider_metadata: null,
        createdAt: '2024-06-18T12:32:59.269Z',
        updatedAt: '2024-06-18T12:32:59.269Z',
      },
    },
  },
  circular_text_line_svg: {
    data: {
      id: 178,
      attributes: {
        name: 'Group 26.svg',
        alternativeText: null,
        caption: null,
        width: 120,
        height: 120,
        formats: null,
        hash: 'Group_26_348828df5c',
        ext: '.svg',
        mime: 'image/svg+xml',
        size: 13.31,
        url: 'https://cdn.marutitech.com/Group_26_348828df5c.svg',
        previewUrl: null,
        provider:
          '@strapi-community/strapi-provider-upload-google-cloud-storage',
        provider_metadata: null,
        createdAt: '2024-10-01T05:09:19.099Z',
        updatedAt: '2024-10-01T05:09:19.099Z',
      },
    },
  },
  testimonials_slider: [
    {
      id: 6,
      clientName: 'Sagar Shah',
      clientDescription:
        '<p>PRESIDENT AND PARTNER AT&nbsp;<br>LIFEWORKS ADVISORS</p>',
      testimonial_video_link:
        'https://player.vimeo.com/video/347119375?h=1699409fe2&color=ef2200&byline=0&portrait=0',
      image: {
        data: {
          id: 41,
          attributes: {
            name: 'image 100 (2).svg',
            alternativeText: null,
            caption: null,
            width: 387,
            height: 300,
            formats: null,
            hash: 'image_100_2_04a7f83d11',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 1962.3,
            url: 'https://cdn.marutitech.com/image_100_2_04a7f83d11.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-18T11:57:29.458Z',
            updatedAt: '2024-06-18T11:57:29.458Z',
          },
        },
      },
    },
    {
      id: 7,
      clientName: 'Sagar Shah',
      clientDescription:
        '<p>PRESIDENT AND PARTNER AT&nbsp;<br>LIFEWORKS ADVISORS</p>',
      testimonial_video_link:
        'https://player.vimeo.com/video/347119375?h=1699409fe2&color=ef2200&byline=0&portrait=0',
      image: {
        data: {
          id: 40,
          attributes: {
            name: 'image 100 (1).svg',
            alternativeText: 'img_2',
            caption: null,
            width: 387,
            height: 300,
            formats: null,
            hash: 'image_100_1_2c8d819efd',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 2058.17,
            url: 'https://cdn.marutitech.com/image_100_1_2c8d819efd.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-18T11:55:12.687Z',
            updatedAt: '2024-06-18T11:58:23.872Z',
          },
        },
      },
    },
    {
      id: 8,
      clientName: 'Sagar Shah',
      clientDescription:
        '<p>PRESIDENT AND PARTNER AT&nbsp;<br>LIFEWORKS ADVISORS</p>',
      testimonial_video_link:
        'https://player.vimeo.com/video/347119375?h=1699409fe2&color=ef2200&byline=0&portrait=0',
      image: {
        data: {
          id: 39,
          attributes: {
            name: 'image 100.svg',
            alternativeText: 'img_3',
            caption: null,
            width: 387,
            height: 300,
            formats: null,
            hash: 'image_100_747020a27d',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 1950.54,
            url: 'https://cdn.marutitech.com/image_100_747020a27d.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-18T11:55:12.332Z',
            updatedAt: '2024-06-18T11:58:03.305Z',
          },
        },
      },
    },
    {
      id: 9,
      clientName: 'Sagar Shah',
      clientDescription:
        '<p>PRESIDENT AND PARTNER AT&nbsp;<br>LIFEWORKS ADVISORS</p>',
      testimonial_video_link:
        'https://player.vimeo.com/video/347119375?h=1699409fe2&color=ef2200&byline=0&portrait=0',
      image: {
        data: {
          id: 41,
          attributes: {
            name: 'image 100 (2).svg',
            alternativeText: null,
            caption: null,
            width: 387,
            height: 300,
            formats: null,
            hash: 'image_100_2_04a7f83d11',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 1962.3,
            url: 'https://cdn.marutitech.com/image_100_2_04a7f83d11.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-18T11:57:29.458Z',
            updatedAt: '2024-06-18T11:57:29.458Z',
          },
        },
      },
    },
    {
      id: 10,
      clientName: 'Sagar Shah',
      clientDescription:
        '<p>PRESIDENT AND PARTNER AT&nbsp;<br>LIFEWORKS ADVISORS</p>',
      testimonial_video_link:
        'https://player.vimeo.com/video/347119375?h=1699409fe2&color=ef2200&byline=0&portrait=0',
      image: {
        data: {
          id: 40,
          attributes: {
            name: 'image 100 (1).svg',
            alternativeText: 'img_2',
            caption: null,
            width: 387,
            height: 300,
            formats: null,
            hash: 'image_100_1_2c8d819efd',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 2058.17,
            url: 'https://cdn.marutitech.com/image_100_1_2c8d819efd.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-18T11:55:12.687Z',
            updatedAt: '2024-06-18T11:58:23.872Z',
          },
        },
      },
    },
  ],
};

export function TestimonialsStory() {
  return <Testimonial data={testimonialData} />;
}
