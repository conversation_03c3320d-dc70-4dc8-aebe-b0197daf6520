@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorThree, oneSpace, fourSpace, eightSpace, grayBorder, gray500, fontWeight400, fontWeight600, fontWeight700, bodyTextXXSmall, halfSpace, twoSpace, threeSpace, fourSpace, fiveSpace, colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.container {
  border-top: 2px solid gray500;
  border-bottom: 2px solid gray500;
  margin-top: fiveSpace;
  padding: 0;
}

.author__wrapper {
  display: flex;
  flex-direction: row;
  gap: 20px;
  justify-content: center;
  padding-top: fiveSpace;
  padding-bottom: fiveSpace;
}

@media screen and (max-width: 576px) {
  .author__wrapper {
    flex-direction: column;
  }
}

.author__image__wrapper {
  padding-left: 10px;
}

.author__image {
  border-radius: 3px;
}

.author__details {
  align-content: center;
}

.author__details .author__headline {
  font-size: 12px;
  line-height: 14px;
  color: #383838;
  padding-top: 4px;
  font-weight: fontWeight400;
}

.author__details .author__name {

  font-size: 24px;
  color: brandColorThree;
  line-height: 29px;
  font-weight: fontWeight600;
}

.author__details .author__desc {
  font-weight: fontWeight400;
  font-size: 12px;
  line-height: 20px;
  max-width: 356px;
  color: colorBlack;
}

.author__details .author__desc p span {
  font-weight: fontWeight400 !important;
  font-size: 12px !important;
  line-height: 20px;
  max-width: 356px;
  color: colorBlack !important;
}

.author__details .link {
  text-decoration: none !important;
}

.author__details .author__post {
  color: brandColorThree;
  font-weight: fontWeight700;
  line-height: 21px;
  font-size: 14px;
  cursor: pointer;
}

.author__details .author__post:hover {
  text-decoration: underline;
  text-underline-offset: 1px;
}

.secondary__container {
  border-bottom: 2px solid grayBorder;
  padding-bottom: eightSpace;
  margin-top: eightSpace;
}

@media screen and (max-width: 576px) {
  .secondary__container {
    font-size: 30px;
    margin-top: 6px;
    width: 95vw;
  }
}

.secondary__wrapper {
  display: flex;
  align-items: center;
}

@media screen and (max-width: 576px) {
  .secondary__wrapper {
    flex-direction: column;
  }
}

.secondary__wrapper .secondary__image img {
  border-radius: 50%;
}

.secondary__wrapper .author__detail__wrappper {
  padding-left: 20px;
}

.secondary__wrapper .author__detail__wrappper .secondary__author__name {

  font-weight: fontWeight600;
  font-size: 34px;
  color: brandColorThree;
  line-height: 123%;
}

@media screen and (max-width: 576px) {
  .secondary__wrapper .author__detail__wrappper .secondary__author__name {
    font-size: 30px;
    margin-top: 6px;
  }
}

.secondary__wrapper .author__detail__wrappper .secondary__description {

  font-style: normal;
  font-weight: fontWeight400;
  font-size: clamp(14px, 1.6vw, 16px);
  line-height: 170%;
  color: colorBlack;
}

.secondary__wrapper .author__detail__wrappper .secondary__description p span {
  font-style: normal !important;
  font-weight: fontWeight400 !important;
  font-size: clamp(14px, 1.6vw, 16px) !important;
  line-height: 170% !important;
  color: colorBlack !important;
}

@media screen and (max-width: 576px) {
  .secondary__wrapper .author__detail__wrappper .secondary__description {
    display: none;
  }

  .secondary__wrapper .author__detail__wrappper .secondary__description__mobile {
    display: block;
    width: 100%;
    font-size: 16px;
  }
}

.secondary__loadmorebtn {
  display: none;
  position: relative;
}

@media screen and (max-width: 576px) {
  .secondary__loadmorebtn {
    display: flex;
    text-align: center;
    justify-content: center;
    font-size: 16px;
    color: brandColorThree;
  }
}

.showmore {
  height: 180px;
  transition: all 0.3s ease;
}

@media screen and (max-width: 484px) {
  .showmore {
    height: calc(100px + 3em);
  }
}

@media screen and (max-width: 350px) {
  .showmore {
    height: calc(110px + 6em);
  }
}

.showless {
  position: relative;
  transition: all 0.3s ease;
  height: 54px;
  overflow: hidden;
}

.fade {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.65) 22.4%, #ffffff 56.77%);
  height: 54px;
  margin-top: -33px;
  position: relative;
  display: none;
}

@media screen and (max-width: 576px) {
  .fade {
    display: block;
  }
}

.arrow_right_loadless {
  transform: rotate(90deg);
  padding-left: 5px;
  margin-left: 2px;
}

.arrow_right_loadmore {
  transform: rotate(270deg);
  margin-left: 4px;
  margin-top: 2px;
}