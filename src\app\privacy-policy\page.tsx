import RichText from '@components/RichText';
import RichResults from '@components/RichResults';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function fetchPrivacyPolicyPageData() {
  return await fetchFromStrapi(
    'privacy-policy',
    'populate=rich_text,seo.schema',
  );
}

export async function generateMetadata({}) {
  const query =
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema';
  const seoFetchedData = await fetchFromStrapi('privacy-policy', query);

  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function PrivacyPolicy() {
  const privacyPolicyPageData = await fetchPrivacyPolicyPageData();
  return (
    <>
      {privacyPolicyPageData?.data?.attributes?.seo && (
        <RichResults data={privacyPolicyPageData?.data?.attributes?.seo} />
      )}
      {privacyPolicyPageData?.data?.attributes?.rich_text && (
        <RichText richTextData={privacyPolicyPageData?.data?.attributes} />
      )}
    </>
  );
}
