@value variables: "@styles/variables.module.css";
@value gray400, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1024, breakpoint-md, breakpoint-sm-450 from breakpoints;

.container {
  display: flex;
  justify-content: center;
  padding: 80px 16px;
  flex-direction: column;
  align-items: center;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 40px 16px;
  }

  background-color: #f3f3f3;
}

.cardWrapper {
  display: flex;
  justify-content: center;
  max-width: 1192px;
  flex-wrap: wrap;
  row-gap: 40px;
  column-gap: 20px;
}

.card_title>h3 {
  color: colorBlack;

  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
  max-width: 384px;

  @media screen and (max-width: breakpoint-md) {
    max-width: 348px;
  }
}

.link {
  width: 384px;
  text-decoration: none;

  @media screen and (max-width: breakpoint-sm-450) {
    width: 100%;
  }
}

.card_preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.text_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: absolute;
  left: 10px;
  top: 106px;
}

.industry_wrapper {
  display: flex;
  padding: 6px 10px;
  border-radius: 6px;
  background-color: colorBlack;
  gap: 10px;
  width: fit-content;
  max-width: 363px;
  flex-wrap: wrap;

  @media screen and (max-width: breakpoint-md) {
    max-width: 327px;
  }
}

.industry_title {
  color: colorWhite;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  padding-right: 10px;
  border-right: 1px solid #cdcdcd;
}

.industry_title:last-child {
  border-right: none;
  padding-right: 0;
}

.previewImage {
  background-color: gray400;
  width: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.pagination {
  display: flex;
  list-style-type: none;
  gap: 30px;
  padding: 0;
  align-items: center;
  color: colorBlack;
  flex-direction: row;
  list-style-image: none;
}

.paginationLink {
  cursor: pointer;
  color: colorBlack;
  text-decoration: none;
}

.paginationDisabled {
  color: #ccc;
  cursor: not-allowed;
  text-decoration: none;
}

.paginationActive {
  background-color: #b41f5e;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50px;
}