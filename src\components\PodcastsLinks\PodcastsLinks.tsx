import Heading from '@components/Heading';
import Image from 'next/image';
import style from './PodcastsLinks.module.css';
import { Container } from 'react-bootstrap';
import Link from 'next/link';

export default function PodcastsLinks({ data }) {
  if (!data?.links?.length) {
    return null;
  }

  return (
    <Container fluid className={style.containerPodcastsLinks}>
      <Heading title={data?.title} headingType="h2" className={style.title} />
      <div className={style.linksWrapper}>
        {data?.links?.map(item => (
          <Link
            key={item?.url}
            href={item?.url}
            target="_blank"
            rel="noopener noreferrer"
            prefetch={false}
          >
            <Image
              src={item?.icon?.data?.attributes?.url}
              alt={
                item?.icon?.data?.attributes?.alternativeText ||
                'Podcast platform icon'
              }
              width={213}
              height={46}
            />
          </Link>
        ))}
      </div>
    </Container>
  );
}
