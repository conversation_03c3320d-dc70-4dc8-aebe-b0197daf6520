@value variables: "@styles/variables.module.css";
@value gray400, gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-xl-1208, breakpoint-xl-1024, breakpoint-md, breakpoint-sm from breakpoints;

.card_title>h2 {
  color: colorBlack;

  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
  max-width: 384px;

  @media screen and (max-width: breakpoint-md) {
    max-width: 348px;
  }
}

.link {
  text-decoration: none;
}

.card_preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.text_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.industry_wrapper {
  display: flex;
  padding: 6px 10px;
  border-radius: 6px;
  background-color: colorBlack;
  gap: 10px;
  width: fit-content;
  max-width: 363px;
  flex-wrap: wrap;

  @media screen and (max-width: breakpoint-md) {
    max-width: 327px;
  }
}

.industry_title {
  color: colorWhite;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.previewImage {
  background-color: gray400;
  border-radius: 6px;

  @media screen and (max-width: breakpoint-md) {
    width: 348px;
  }
}

.eventsListing {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 80px;
  max-width: 1440px;

  @media screen and (max-width: 1300px) {
    padding: 40px 32px;
    justify-content: center;
  }

  @media screen and (max-width: 450px) {
    padding: 40px 0;
  }
}