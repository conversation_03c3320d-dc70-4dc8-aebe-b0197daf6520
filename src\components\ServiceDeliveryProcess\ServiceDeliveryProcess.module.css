@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-md-767, breakpoint-xl-1440, breakpoint-sm-390 from breakpoints;

.container {
  padding: 5rem 9rem;
  background-color: gray300;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 0;
  }
}

.heading > h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 140%;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.embla {
  max-width: 76rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 20px;
  --slide-size: 25%;

  @media (max-width: breakpoint-xl-1440) {
    max-width: 74.5rem;
    --slide-size: 25%;
  }

  @media (max-width: breakpoint-md-767) {
    max-width: auto;
    --slide-size: 100%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 320px;
    --slide-size: 100%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  gap: 10;
  touch-action: pan-y pinch-zoom;
  /* margin-left: calc(var(--slide-spacing) * -1); */
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding: 0 calc(var(--slide-spacing) / 2);
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.cardWrapper {
  color: colorWhite;
  padding: 40px 0;
  display: flex;
}

.card {
  width: 282px;
  height: 100%;
  display: flex;
  padding: 40px 0;
  flex-direction: column;
  gap: 29px;
  border-radius: 6px;
  color: colorWhite;
  background-color: colorBlack;
  background-image: url(https://cdn.marutitech.com/Group_5043_dc21af6bd6.svg);
  background-repeat: no-repeat;
  padding: 1.5rem;
  text-align: left;
  user-select: none;
}

@media (min-width: breakpoint-md) {
  .card:hover {
    .card__description {
      transform: translateY(-10px);
    }
  }
}

.cardTop {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.card__number {
  width: 32px;
  position: relative;
  padding: 2px 6px;
  text-align: center;

  font-size: 16px;
  font-weight: 400;
  font-style: normal;
  line-height: normal;
}

.card__number::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 3px;
  padding: 1.5px;
  background: linear-gradient(
    93.12deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.card__title > h3 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
}

.card__description {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  transition: transform 0.3s ease;
}
