import HeroSectionPodcasts from '@components/HeroSectionPodcasts';
import LatestEpisode from '@components/LatestEpisode';
import PodcastsLinks from '@components/PodcastsLinks';
import PodcastsSeries from '@components/PodcastsSeries';
import CTA from '@components/CTA';
import ContactUsForm from '@components/ContactUsForm';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function fetchPodcastsPageData() {
  const queryString = `populate=hero_section.image&populate=listen_on.links.icon&populate=play_button_across_page&populate=podcast_series.podcast_episode.video_thumbnail_image,podcast_series.podcast_episode.podcast_audio_file&populate=latest_episode.video_thumbnail_image&populate=CTA&populate=form.formFields,form.button,seo.schema`;

  const response = await fetchFromStrapi('podcast-page', queryString);
  return response;
}

async function getFormData() {
  const queryString = 'populate=form.formFields&populate=form.button';
  return await fetchFromStrapi('form', queryString);
}

export async function generateMetadata({}) {
  const queryString = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('podcast-page', queryString);

  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}
export default async function Podcasts() {
  const podcastsPageData = await fetchPodcastsPageData();
  const formData = await getFormData();

  return (
    <>
      {podcastsPageData?.data?.attributes?.seo && (
        <RichResults data={podcastsPageData?.data?.attributes?.seo} />
      )}
      {podcastsPageData?.data?.attributes?.hero_section && (
        <HeroSectionPodcasts
          data={podcastsPageData?.data?.attributes?.hero_section}
        />
      )}
      {podcastsPageData?.data?.attributes?.listen_on && (
        <PodcastsLinks data={podcastsPageData?.data?.attributes?.listen_on} />
      )}
      {podcastsPageData?.data?.attributes?.latest_episode && (
        <LatestEpisode
          data={podcastsPageData?.data?.attributes?.latest_episode}
          playButtonUrl={
            podcastsPageData?.data?.attributes?.play_button_across_page?.data
              ?.attributes?.url
          }
        />
      )}
      {podcastsPageData?.data?.attributes?.podcast_series && (
        <PodcastsSeries
          PodcastsSeriesData={
            podcastsPageData?.data?.attributes?.podcast_series
          }
          playButtonUrl={
            podcastsPageData?.data?.attributes?.play_button_across_page?.data
              ?.attributes?.url
          }
        />
      )}
      {podcastsPageData?.data?.attributes?.CTA && (
        <CTA
          data={podcastsPageData?.data?.attributes?.CTA}
          variant="scrollToContactForm"
        />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Podcasts"
        />
      )}
    </>
  );
}
