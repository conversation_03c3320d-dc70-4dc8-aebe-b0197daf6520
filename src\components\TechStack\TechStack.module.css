@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px, breakpoint-xl-2560 from breakpoints;

.ourServiceTitle > h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 138%;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.main_container {
  padding: 80px 124px;
  display: flex;
  flex-direction: column;
  gap: 40px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }
}

.inner_container {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.scrollContainer {
  display: flex;
  align-items: center;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  cursor: grab;
}

.scrollContainer:active {
  cursor: grabbing;
}

.scrollContainer::-webkit-scrollbar {
  height: 5px;
}

.scrollContainer::-webkit-scrollbar-track {
  background: transparent;
}

.scrollContainer::-webkit-scrollbar-thumb {
  background: linear-gradient(
    93.12deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );
  border-radius: 10px;
}

.scrollContent {
  display: flex;
  gap: 20px;
  padding-bottom: 20px;
}

.scrollWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.scrollButton {
  padding: 10px;
  margin-bottom: 24px;
}

.tech_tabs_wrapper {
  padding-right: 24px;
  border-right: 1px solid #e4e4e4;

  @media screen and (max-width: breakpoint-sm-450) {
    display: flex;
  }
}

.tech_tabs_wrapper:last-child {
  border-right: none;
}

.tech_tabs {
  padding: 16px 24px;
  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;
  user-select: none;
  border-radius: 6px;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: breakpoint-sm-450) {
    text-wrap: wrap;
    width: 240px;
  }
}

.activeTab {
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(
      93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.iconContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(228, 228, 228, 0.3);
  border-radius: 5px;
  text-align: center;
  width: 95px;
  height: 98px;
}

.iconImage {
  width: 78px;
  height: 78px;
  border-radius: 10px;
  margin: 10px;
}

.iconTitle {
  text-align: center;

  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;
}

.tabContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px 100px;
  padding-top: 40px;

  @media screen and (max-width: breakpoint-sm-550) {
    gap: 30px;
  }
}

.box {
  flex: 1 0 calc(15% - 40px);
  max-width: calc(15% - 40px);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;

  @media screen and (max-width: breakpoint-sm-550) {
    flex: 1 0 calc(50% - 40px);
    max-width: calc(50% - 40px);
  }

  @media screen and (min-width: breakpoint-xl-2000) {
    flex-basis: calc(7% - 40px);
    max-width: calc(7% - 40px);
  }
}
