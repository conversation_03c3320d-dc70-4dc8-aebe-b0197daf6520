import React from 'react';
import AwardsRecognition from '@components/AwardsRecognition';
import CaseStudyCard from '@components/CaseStudyCard';
import CTA from '@components/CTA';
import Insights from '@components/Insights';
import HeroSection from '@components/HeroSection';
import OtherServicesCard from '@components/OtherServicesCard';
import ServicesCard from '@components/ServicesCard';
import Testimonial from '@components/Testimonial';
import TrustedPartners from '@components/TrustedPartners';
import WhyChooseMTL from '@components/WhyChooseMTL';
import ServiceDeliveryProcess from '@components/ServiceDeliveryProcess';
import TechStack from '@components/TechStack';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import ContactUsForm from '@components/ContactUsForm';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

export async function generateStaticParams() {
  const response = await fetchFromStrapi(
    'l3-services-pages',
    'populate=l_2_service_page',
  );
  const l3Paths = response?.data?.map(res => ({
    service: res?.attributes?.l_2_service_page?.data?.attributes?.slug,
    l3service: res.attributes.slug,
  }));

  return l3Paths;
}

export async function fetchL3ServiceData(slug: string) {
  const query = `filters[slug][$eq]=${slug}&populate=l_2_service_page&populate=whyChooseMtl,whyChooseMtl.whyChooseMtlCards&populate=hero_section,hero_section.image,hero_section.mobile_image&populate=cta&populate=service_offering_card,service_offering_card.L2ServicesCard,service_offering_card.L2ServicesCard.on_hover_bg_image&populate=industry_awards,industry_awards.awards_box,industry_awards.awards_box.image,&populate=case_study.card_box.image&populate=testimonials,testimonials.testimonials_slider,testimonials.testimonials_slider.image,testimonials_slider.testimonial_video_link,testimonials.testimonial_playbtn_logo,testimonials.circular_text_line_svg&populate=our_service_delivery_process.cards.number&populate=insights,insights.circular_text_image,insights.blogs.heroSection_image&populate=other_services.l3_other_services_card,other_services.other_services_card.on_hover_bg_image&populate=tech_stack,tech_stack.tab.logo_url&populate=case_study_cards.case_study_relation.preview.preview_background_image&populate=case_study_cards.case_study_relation.hero_section.global_services,cta_2,seo.schema`;
  return await fetchFromStrapi('l3-services-pages', query);
}

async function getTrustedPartnersData() {
  const query = `populate=trustedPartner.title&populate=trustedPartner.partnersLogo.images`;
  return await fetchFromStrapi('trusted-partner', query);
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

async function getTestimonialsData() {
  const query = `populate=testimonials,testimonials.testimonials_slider,testimonials.testimonials_slider.image,testimonials_slider.testimonial_video_link,testimonials.testimonial_playbtn_logo,testimonials.circular_text_line_svg`;
  return await fetchFromStrapi('testimonial', query);
}

async function getAwardsData() {
  const query = `populate=awards.awards_box.image`;
  return await fetchFromStrapi('award', query);
}

export async function generateMetadata({
  params,
}: {
  params: { l3service: string };
}) {
  const { l3service: slug } = params;

  const query = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('l3-services-pages', query);

  const seoData = seoFetchedData?.data[0]?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function L3Services({
  params,
}: {
  params: { l3service: string };
}) {
  const { l3service } = params;

  const L3data = await fetchL3ServiceData(l3service);
  const trustedPartnersData = await getTrustedPartnersData();
  const formData = await getFormData();
  const testimonials = await getTestimonialsData();
  const awards = await getAwardsData();

  // Check if L3 service page data exists, otherwise return 404
  if (!L3data?.data || L3data?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {L3data?.data[0]?.attributes?.seo && (
        <RichResults data={L3data?.data[0]?.attributes?.seo} />
      )}
      {L3data?.data[0]?.attributes?.hero_section && (
        <HeroSection
          heroData={L3data?.data[0]?.attributes?.hero_section}
          variant="primary"
          L2pageName={
            L3data?.data[0]?.attributes?.l_2_service_page?.data?.attributes
              ?.pageName
          }
          L2pageSlug={
            L3data?.data[0]?.attributes?.l_2_service_page?.data?.attributes
              ?.slug
          }
          L3pageName={L3data?.data[0]?.attributes?.pageName}
          L3pageSlug={L3data?.data[0]?.attributes?.slug}
        />
      )}
      {trustedPartnersData?.data?.attributes?.trustedPartner && (
        <TrustedPartners
          data={trustedPartnersData?.data?.attributes?.trustedPartner}
        />
      )}
      {L3data?.data[0]?.attributes?.cta && (
        <CTA
          data={L3data?.data[0]?.attributes?.cta}
          variant="scrollToContactForm"
        />
      )}
      {L3data?.data[0]?.attributes?.service_offering_card && (
        <ServicesCard
          l2ServiceData={L3data?.data[0]?.attributes?.service_offering_card}
          variant="blackSlideCard"
        />
      )}
      {L3data?.data[0]?.attributes?.whyChooseMtl && (
        <WhyChooseMTL data={L3data?.data[0]?.attributes?.whyChooseMtl} />
      )}
      {awards?.data?.attributes?.awards && (
        <AwardsRecognition
          awardsRecognitionData={awards?.data?.attributes?.awards}
        />
      )}
      {L3data?.data[0]?.attributes?.cta_2 && (
        <CTA
          data={L3data?.data[0]?.attributes?.cta_2}
          variant="scrollToContactForm"
        />
      )}
      {L3data?.data[0]?.attributes?.case_study_cards && (
        <CaseStudyCard
          variantWhite
          case_study={L3data?.data[0]?.attributes?.case_study_cards}
        />
      )}
      {testimonials?.data?.attributes?.testimonials && (
        <Testimonial data={testimonials?.data?.attributes?.testimonials} />
      )}
      {L3data?.data[0]?.attributes?.tech_stack && (
        <TechStack data={L3data?.data[0]?.attributes?.tech_stack} />
      )}
      {L3data?.data[0]?.attributes?.our_service_delivery_process && (
        <ServiceDeliveryProcess
          data={L3data?.data[0]?.attributes?.our_service_delivery_process}
        />
      )}
      {L3data?.data[0]?.attributes?.insights && (
        <Insights data={L3data?.data[0]?.attributes?.insights} />
      )}
      {L3data?.data[0]?.attributes?.other_services && (
        <OtherServicesCard
          variant="L3Services"
          data={L3data?.data[0]?.attributes?.other_services}
        />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="L3Services"
        />
      )}
    </>
  );
}
