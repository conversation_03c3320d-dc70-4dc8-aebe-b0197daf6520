export interface OtherServiceCard {
  id: number;
  description: string;
  service_page_link: string;
  title: string;
  on_hover_bg_image: {
    data: {
      id: number;
      attributes: {
        alternativeText?: string;
        caption?: string;
        createdAt?: string;
        ext?: string;
        formats?: null;
        hash?: string;
        height?: number;
        mime?: string;
        name?: string;
        previewUrl?: null;
        provider?: string;
        provider_metadata?: null;
        size?: number;
        updatedAt?: string;
        url?: string;
        width?: number;
      };
    };
  };
}

export interface OtherServicesCardTypes {
  id: number;
  title: string;
  all_services_card_title: string;
  all_services_card_link: string;
  other_services_card: OtherServiceCard[];
}
