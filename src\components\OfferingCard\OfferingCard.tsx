'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import emblastyles from '../../styles/emlaDots.module.css';
import classNames from '@utils/classNames';

import styles from './OfferingCard.module.css';

export default function OfferingCard({ data, variantWhite = true }) {
  const [emblaRef, emblaApi] = useEmblaCarousel();
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isMobile = useMediaQueryState({
    query: `(max-width: 768px)`,
  });
  return (
    <>
      {!isMobile && (
        <Container fluid className={styles.main_container}>
          <div className={styles.container}>
            <h4 className={styles.heading}>{data?.title}</h4>
            <div className={styles.card_box}>
              {data?.card.map((data, index: number) => (
                <div className={styles.card} key={index}>
                  <h3 className={styles.card__title}>{data?.card_title}</h3>
                  <div
                    className={styles.card__description}
                    dangerouslySetInnerHTML={{
                      __html: data?.card_description,
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </Container>
      )}
      {isMobile && (
        <Container fluid className={styles.container_embla}>
          <Heading headingType="h4" title={data?.title} position="center" />
          <div className={styles.embla}>
            <div className={styles.embla__viewport} ref={emblaRef}>
              <div className={styles.embla__container}>
                {data?.card.map((data, index: number) => (
                  <div className={styles.cardWrapper} key={index}>
                    <div className={styles.embla__slide} key={1}>
                      <div className={styles.card} key={index}>
                        <h3 className={styles.card__title}>
                          {data?.card_title}
                        </h3>
                        <div
                          className={styles.card__description}
                          dangerouslySetInnerHTML={{
                            __html: data?.card_description,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className={styles.embla__controls}>
            <div className={emblastyles.embla__dots}>
              {scrollSnaps.length > 1 &&
                scrollSnaps.map((_, index) => (
                  <DotButton
                    key={index}
                    onClick={() => onDotButtonClick(index)}
                    className={
                      index === selectedIndex
                        ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                        : variantWhite
                          ? classNames(
                              emblastyles.embla__dot,
                              emblastyles.embla__dot_bg_white,
                            )
                          : emblastyles.embla__dot
                    }
                  />
                ))}
            </div>
          </div>
        </Container>
      )}
    </>
  );
}
