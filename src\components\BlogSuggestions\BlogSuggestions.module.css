@value variables: "@styles/variables.module.css";
@value gray900, oneSpace, fontWeight500, fontWeight700, bodyTextXXSmall, halfSpace, twoSpace, threeSpace, fourSpace, fiveSpace, colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

/* Small screens Styles <577px (Embla-Carousel) */
.embla {
  max-width: 48rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 55%;

  @media screen and (max-width: breakpoint-xl) {
    max-width: 44rem;
    --slide-size: 47%;
  }

  @media (max-width: breakpoint-md-767) {
    max-width: 35rem;
    --slide-size: 70%;
  }

  @media (max-width: breakpoint-sm-550) {
    max-width: 384px;
    --slide-size: 100%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 300px;
    --slide-size: 100%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  gap: 10;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  justify-items: center;
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
  margin-bottom: 40px;
}

.mobileDots {
  display: block;
}

/* Big screens styles >576px */
.mainContainer {
  padding: 40px 0px;
  display: flex;
  gap: 10px;
  justify-content: center;
  justify-items: center;
  height: 100%;
  width: auto;
}

.box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  width: 316px;
  height: 370px;
  text-decoration: none;
}

.box_additional_mobile_styles {
  margin: 15px;
  box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.25);
}

.box:hover {
  box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.25);
}

.blogDetails {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.blogSuggestionsCoverImage {
  width: 100%;
}

.category {
  color: gray900;
  font-size: 10px;
  font-style: normal;
  font-weight: fontWeight500;
  line-height: 127%;
}

.title {
  color: gray900;

  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 140%;
}

.description {
  color: gray900;
  font-size: 10px;
  font-style: normal;
  font-weight: fontWeight500;
  line-height: 127%;
}

.authorSection {
  display: flex;
  align-items: center;
  gap: 5px;
}

.authorCoverImage {
  border-radius: 50%;
}

.authorName {
  color: gray900;

  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 123%;
}

.authorDesignation {
  color: gray900;
  font-size: 10px;
  font-style: normal;
  font-weight: fontWeight500;
  line-height: 127%;
}

.image_content_column1 {
  background-repeat: no-repeat;
  background-size: cover;
}

.column2 {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 10px;
  filter: drop-shadow(0px 0px 0px rgba(0, 0, 0, 0));
}

.column2_div {
  position: absolute;
  left: -5rem;
  background-color: colorWhite;
  border-radius: 3px;
  padding: 30px;
  right: 5rem;
}

.column2_div_row1 {
  display: flex;
  justify-content: space-between;
}

.column2_div .category {
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 14px;
  line-height: 123%;
  color: black;
  padding: 5px;
  background: brandColorOne;
  border-radius: 5px;
}

.column2_div .blog_title {
  margin-top: 20px;
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 36px;
  line-height: 115%;
  color: gray900;
}

.column2_div .blog_description {
  margin-top: 20px;
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 16px;
  line-height: 180%;
  color: gray900;
}

.mobile_image_div {
  height: 465px;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.mobile_content_wrapper_div {
  border-radius: 10px;
  filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.25));
  padding: 25px;
  background: colorWhite;
  width: 94%;
  margin: -110px auto 0 auto;

  .table_of_contents {
    margin-top: 40px;
    font-style: normal;
    font-weight: fontWeight700;
    font-size: 18px;
    line-height: 150%;
    color: #f05443;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table_of_contents span {
    margin-left: 10px;
  }
}

.mobile_content_wrapper_div .div_row1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile_content_wrapper_div .category {
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 14px;
  line-height: 123%;
  color: colorBlack;
  padding: 5px;
  background: brandColorOne;
  border-radius: 5px;
}

.mobile_content_wrapper_div .time {
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 14px;
  line-height: 123%;
  text-align: right;
  color: gray900;
}

.mobile_content_wrapper_div .blog_title {
  margin-top: 10px;

  font-style: normal;
  font-weight: fontWeight700;
  font-size: 30px;
  line-height: 123%;
  color: gray900;
}

.mobile_content_wrapper_div .blog_description {
  margin-top: 10px;
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 16px;
  line-height: 123%;
  color: gray900;
}

.mobile_content_wrapper_div .author {
  display: flex;
  align-items: center;
  margin-top: 30px;
}