// 'use client';

// import Image from 'next/image';
// import useMediaQueryState from '@hooks/useMediaQueryState';

// export default function ImageWithSizing({
//   src,
//   width,
//   height,
//   fill = false,
//   alt = 'image alt text',
//   loading = 'lazy',
//   useThumbnail = false,
//   className,
//   style,
// }: {
//   src: any;
//   width?: any;
//   height?: any;
//   fill?: any;
//   alt?: any;
//   loading?: any;
//   useThumbnail?: any;
//   className?: any;
//   style?: any;
// }) {
//   const srcSmall = useMediaQueryState({
//     query: '(max-width: 576px)',
//   });
//   const srcMedium = useMediaQueryState({
//     query: '(max-width: 768px)',
//   });
//   const srcXL = useMediaQueryState({
//     query: '(max-width: 1600px)',
//   });
//   return (
//     <>
//       <Image
//         src={
//           useThumbnail
//             ? src?.format?.small?.url || src?.formats?.small?.url || src?.url
//             : srcSmall
//               ? src?.format?.small?.url || src?.formats?.small?.url || src?.url
//               : srcMedium
//                 ? src?.format?.medium?.url ||
//                   src?.formats?.medium?.url ||
//                   src?.url
//                 : //   : srcXL
//                   //     ? src?.format?.large?.url ||
//                   //       src?.formats?.large?.url ||
//                   //       src?.url
//                   src?.url
//         }
//         alt={alt}
//         width={width}
//         height={height}
//         fill={fill}
//         loading={loading}
//         className={className}
//         style={style}
//       ></Image>
//     </>
//   );
// }

'use client';

import Image from 'next/image';
import useMediaQueryState from '@hooks/useMediaQueryState';
import { useEffect, useState } from 'react';

export default function ImageWithSizing({
  src,
  width,
  height,
  fill = false,
  alt = 'image alt text',
  loading = 'lazy',
  useThumbnail = false,
  className,
  style,
}: {
  src: any;
  width?: any;
  height?: any;
  fill?: any;
  alt?: any;
  loading?: any;
  useThumbnail?: any;
  className?: any;
  style?: any;
}) {
  const isSmall = useMediaQueryState({ query: '(max-width: 576px)' });
  const isMedium = useMediaQueryState({ query: '(max-width: 768px)' });
  const isXL = useMediaQueryState({ query: '(max-width: 1600px)' });

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true); // Ensures this only runs on the client
  }, []);

  if (!mounted) return null; // Prevent server-side image rendering

  const resolvedSrc = useThumbnail
    ? src?.format?.small?.url || src?.formats?.small?.url || src?.url
    : isSmall
      ? src?.format?.small?.url || src?.formats?.small?.url || src?.url
      : isMedium
        ? src?.format?.medium?.url || src?.formats?.medium?.url || src?.url
        : src?.url;

  return (
    <Image
      unoptimized
      src={resolvedSrc}
      alt={alt}
      width={width}
      height={height}
      fill={fill}
      loading={loading}
      className={className}
      style={style}
    />
  );
}
