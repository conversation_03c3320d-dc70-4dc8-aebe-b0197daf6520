import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

// Test page to verify mobile image functionality with enhanced ImageWithBlurPreview
export default function TestMobileResponsive() {
  // Mock data structure similar to what Strapi returns
  const mockDesktopImage = {
    url: 'https://via.placeholder.com/1920x1080/0066cc/ffffff?text=Desktop+Image',
    alternativeText: 'Desktop Hero Image',
    formats: {
      large: {
        url: 'https://via.placeholder.com/1920x1080/0066cc/ffffff?text=Desktop+Large'
      },
      medium: {
        url: 'https://via.placeholder.com/1200x675/0066cc/ffffff?text=Desktop+Medium'
      },
      small: {
        url: 'https://via.placeholder.com/800x450/0066cc/ffffff?text=Desktop+Small'
      },
      thumbnail: {
        url: 'https://via.placeholder.com/200x113/0066cc/ffffff?text=Desktop+Thumb'
      }
    }
  };

  const mockMobileImage = {
    url: 'https://via.placeholder.com/768x1024/cc6600/ffffff?text=Mobile+Image',
    alternativeText: 'Mobile Hero Image',
    formats: {
      large: {
        url: 'https://via.placeholder.com/768x1024/cc6600/ffffff?text=Mobile+Large'
      },
      medium: {
        url: 'https://via.placeholder.com/600x800/cc6600/ffffff?text=Mobile+Medium'
      },
      small: {
        url: 'https://via.placeholder.com/400x533/cc6600/ffffff?text=Mobile+Small'
      },
      thumbnail: {
        url: 'https://via.placeholder.com/150x200/cc6600/ffffff?text=Mobile+Thumb'
      }
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Enhanced ImageWithBlurPreview Mobile Test</h1>
      
      <div style={{ marginBottom: '40px' }}>
        <h2>Test 1: With Mobile Image</h2>
        <p>Desktop: Blue image (screens > 768px), Mobile: Orange image (screens ≤ 768px)</p>
        <div style={{ position: 'relative', width: '100%', height: '400px', border: '2px solid #ccc', marginBottom: '20px' }}>
          <ImageWithBlurPreview
            data={mockDesktopImage}
            mobileData={mockMobileImage}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass="test-image"
          />
        </div>
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>Test 2: Without Mobile Image (Fallback)</h2>
        <p>Should show blue image on both desktop and mobile</p>
        <div style={{ position: 'relative', width: '100%', height: '400px', border: '2px solid #ccc', marginBottom: '20px' }}>
          <ImageWithBlurPreview
            data={mockDesktopImage}
            mobileData={null}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass="test-image"
          />
        </div>
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>Test 3: Original Usage (No mobileData prop)</h2>
        <p>Should work exactly like before - blue image on all devices</p>
        <div style={{ position: 'relative', width: '100%', height: '400px', border: '2px solid #ccc', marginBottom: '20px' }}>
          <ImageWithBlurPreview
            data={mockDesktopImage}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass="test-image"
          />
        </div>
      </div>

      <style jsx>{`
        .test-image {
          object-fit: cover;
        }
      `}</style>
    </div>
  );
}
