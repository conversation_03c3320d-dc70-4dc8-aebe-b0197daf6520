'use client';

import React from 'react';
import Image from 'next/image';
import { EmblaOptionsType } from 'embla-carousel';
import AutoScroll from 'embla-carousel-auto-scroll';
import useEmblaCarousel from 'embla-carousel-react';
import classNames from '@utils/classNames';

import styles from './TrustedPartners.module.css';
import { TrustedPartnerSlideTypes, TrustedPartnersTypes } from './types';
import ImageWithSizing from '@components/ImageWithSizing';

export default function TrustedPartners({ data }: TrustedPartnersTypes) {
  const OPTIONS: EmblaOptionsType = {
    loop: true,
    dragFree: false,
    skipSnaps: false,
  };
  const [emblaRef] = useEmblaCarousel(OPTIONS, [
    AutoScroll({
      playOnInit: true,
      speed: 1,
      stopOnFocusIn: false,
      stopOnMouseEnter: false,
      stopOnInteraction: false,
    }),
  ]);

  return (
    <div className={styles.trustedPartnersWrapper}>
      <div className={styles.heading}>{data?.title}</div>
      <div className={styles.embla}>
        <div className={styles.embla__viewport} ref={emblaRef}>
          <div
            className={classNames(styles.embla__container, styles.logoWrapper)}
          >
            {data?.partnersLogo?.images?.data?.map(
              (logos: TrustedPartnerSlideTypes, index: number) => (
                <div className={styles.embla__slide} key={index}>
                  <div
                    className={classNames(
                      styles.embla__slide__numberembla__slide__number,
                    )}
                  >
                    <ImageWithSizing
                      src={logos?.attributes}
                      alt={logos?.attributes?.alternativeText}
                      height={logos?.attributes?.height}
                      width={logos?.attributes?.width}
                    />
                  </div>
                </div>
              ),
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
