'use client';

import { useEffect, useState } from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import useDotButton from '@hooks/useDotButton';

import emblastyles from '../../styles/emlaDots.module.css';
import styles from './AwardsRecognition.module.css';
import Heading from '@components/Heading';
import ImageWithSizing from '@components/ImageWithSizing';

// Helper function to group cards into sets
const groupAwards = (awards, itemsPerSlide) => {
  const grouped = [];
  for (let i = 0; i < awards.length; i += itemsPerSlide) {
    grouped.push(awards.slice(i, i + itemsPerSlide));
  }
  return grouped;
};

export default function AwardsRecognition({ awardsRecognitionData }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    dragFree: true,
  });
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const [itemsPerSlide, setItemsPerSlide] = useState(1); // Default to 1 for smaller screens

  // Update itemsPerSlide based on screen size
  useEffect(() => {
    const updateItemsPerSlide = () => {
      if (window.innerWidth > 1024) {
        setItemsPerSlide(4);
      } else {
        setItemsPerSlide(1);
      }
    };

    updateItemsPerSlide(); // Initial check
    window.addEventListener('resize', updateItemsPerSlide);

    return () => window.removeEventListener('resize', updateItemsPerSlide);
  }, []);

  // Group awards dynamically based on screen size
  const groupedAwards = groupAwards(
    awardsRecognitionData?.awards_box || [],
    itemsPerSlide,
  );

  return (
    <Container fluid className={styles.main_container}>
      <div className={styles.inner_container}>
        <Heading
          headingType="h2"
          title={awardsRecognitionData?.title}
          className={styles.main_title}
        />
        <section className={styles.embla}>
          <div className={styles.embla__viewport} ref={emblaRef}>
            <div className={styles.embla__container}>
              {groupedAwards.map((group, index) => (
                <div className={styles.embla__slide} key={index}>
                  <div className={styles.card_box}>
                    {group.map(data => (
                      <div className={styles.card} key={data?.id}>
                        <div className={styles.image_box}>
                          <ImageWithSizing
                            src={data?.image?.data?.attributes}
                            alt={data?.image?.data?.attributes?.alternativeText}
                            width={140}
                            height={140}
                          />
                        </div>
                        <div className={styles.line} />
                        <Heading
                          headingType="h3"
                          title={data?.title}
                          className={styles.awards_title}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div
            className={`${emblastyles.embla__controls} ${styles.embla__controls}`}
          >
            <div className={emblastyles.embla__dots}>
              {scrollSnaps.length > 1 &&
                scrollSnaps.map((_, index) => (
                  <DotButton
                    key={index}
                    onClick={() => onDotButtonClick(index)}
                    className={
                      index === selectedIndex
                        ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                        : emblastyles.embla__dot
                    }
                  />
                ))}
            </div>
          </div>
        </section>
      </div>
    </Container>
  );
}
