@value variables: "@styles/variables.module.css";
@value colorBlack from variables;

.iframe,
.thumbnail {
  background-color: colorBlack;
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.thumbnail:hover {
  cursor: pointer;
}

.playButton {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.playButton:hover {
  cursor: pointer;
}
