import styles from './ContactUs.module.css';
import AwardsCarousel from '@components/AwardsCarousel';
import Form from '@components/Form';
import Heading from '@components/Heading';
import Image from 'next/image';
import Link from 'next/link';

export default function ContactUs({ dataContactUs, source = 'HomePage' }) {
  return (
    <div className={styles.mainContainer}>
      {dataContactUs?.left_section_content && (
        <div className={styles.formContainer}>
          <div className={styles.textBox}>
            <Heading
              headingType="h1"
              title={dataContactUs?.left_section_content?.title}
              className={styles.title}
            />
            <div
              className={styles.description}
              dangerouslySetInnerHTML={{
                __html: dataContactUs?.left_section_content?.description,
              }}
            ></div>
          </div>
          <Form
            fields={dataContactUs?.left_section_content?.left_side_fields}
            button={dataContactUs?.left_section_content?.button_title}
            text={dataContactUs?.left_section_content?.consent_text}
            source={source}
          />
        </div>
      )}
      {dataContactUs?.right_section_content && (
        <div className={styles.rightContainer}>
          <Image
            className={styles.gradientImageTop}
            src="https://cdn.marutitech.com/contact_us_top_gradient_98c786f21b.png"
            width={360}
            height={420}
            alt="gradient background"
            draggable="false"
          />
          <div className={styles.exceptCarousel}>
            <Image
              className={styles.logo}
              src={
                dataContactUs?.right_section_content?.logo?.data?.attributes
                  ?.url
              }
              width={200}
              height={38.89}
              alt="logo"
            ></Image>
            <div className={styles.flexContainer}>
              <div className={styles.trustedBy}>
                <span>
                  {dataContactUs?.right_section_content?.trusted_by_title}
                </span>
                <div className={styles.companies}>
                  {dataContactUs?.right_section_content?.trusted_by_logos?.images?.data.map(
                    (img: any) => (
                      <Image
                        key={img?.id}
                        src={img?.attributes?.url}
                        width={105}
                        height={115}
                        alt="company logo"
                        draggable="false"
                      />
                    ),
                  )}
                </div>
              </div>
              <div className={styles.infoContainer}>
                <div className={styles.textCard}>
                  <Heading
                    headingType="h2"
                    title={
                      dataContactUs?.right_section_content?.collabration_title
                    }
                    className={styles.textCardHeading}
                  />
                  <div className={styles.content}>
                    {dataContactUs?.right_section_content?.collab_box.map(
                      box => (
                        <div className={styles.collabBox} key={box?.id}>
                          <div className={styles.collab_title}>
                            {box?.title}
                          </div>
                          <span key={box?.id} className={styles.text}>
                            <Link
                              href={`mailto:${box?.email_id}`}
                              className={styles.link}
                            >
                              {box?.email_id}
                            </Link>
                          </span>
                        </div>
                      ),
                    )}
                  </div>
                </div>
                <div className={styles.textCard}>
                  <Heading
                    headingType="h2"
                    title={
                      dataContactUs?.right_section_content
                        ?.request_a_discovery_call
                    }
                    className={styles.textCardHeading}
                  />
                  <div className={styles.content}>
                    {dataContactUs?.right_section_content?.request_a_discovery_call_box.map(
                      box => (
                        <span key={box?.id} className={styles.text}>
                          <Link
                            href={`tel:${box?.title}`}
                            className={styles.link}
                          >
                            {box?.title}
                          </Link>
                        </span>
                      ),
                    )}
                  </div>
                </div>

                <div className={styles.textCard}>
                  <Heading
                    headingType="h2"
                    title={
                      dataContactUs?.right_section_content?.our_offices?.title
                    }
                    className={styles.textCardHeading}
                  />
                  <div className={styles.content}>
                    <div
                      className={styles.text}
                      dangerouslySetInnerHTML={{
                        __html:
                          dataContactUs?.right_section_content?.our_offices
                            ?.india,
                      }}
                    ></div>
                    <div
                      className={styles.text}
                      dangerouslySetInnerHTML={{
                        __html:
                          dataContactUs?.right_section_content?.our_offices
                            ?.usa,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <Heading
              headingType="h2"
              title={dataContactUs?.right_section_content?.form_awards?.title}
              className={styles.textCardHeading}
            />
          </div>
          {dataContactUs?.right_section_content?.form_awards && (
            <AwardsCarousel
              dataAwards={dataContactUs?.right_section_content?.form_awards}
            />
          )}
          <Image
            className={styles.gradientImageBottom}
            src="https://cdn.marutitech.com/contact_us_bottom_gradient_cac5e3071e.png"
            width={464}
            height={452}
            alt="gradient background"
            draggable="false"
          />
        </div>
      )}
    </div>
  );
}
