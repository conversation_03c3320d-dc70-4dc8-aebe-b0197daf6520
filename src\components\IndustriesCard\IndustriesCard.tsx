/* eslint-disable react/prop-types */

'use client';

import React, { useCallback } from 'react';
import { Container } from 'react-bootstrap';
import Link from 'next/link';
import Autoplay from 'embla-carousel-autoplay';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import CircularButtonWithArrow from '@components/CircularButtonWithArrow';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import emblastyles from '../../styles/emlaDots.module.css';
import { DotButton, useDotButton } from '../HomeHeroSection/CarouselDotButton';
import styles from './IndustriesCard.module.css';
import Heading from '@components/Heading';

export default function StatisticsCard({ industriesCardData }: any) {
  // eslint-disable-next-line react/prop-types, @typescript-eslint/no-explicit-any
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true }, [
    Fade(),
    Autoplay({
      delay: 6000,
      stopOnInteraction: true,
    }),
  ]);

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);
  const isMobile = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-sm']})`,
  });

  const { title, industriesCardsBox }: any = industriesCardData;
  const firstBoxBgImage = {
    '--first-box-bg-image': `url(${industriesCardsBox[0]?.backgroundImage?.data?.attributes?.format?.large?.url || industriesCardsBox[0]?.backgroundImage?.data?.attributes?.formats?.large?.url || industriesCardsBox[0]?.backgroundImage?.data?.attributes?.url})`,
  } as React.CSSProperties;

  const secondBoxBgImage = {
    '--second-box-bg-image': `url(${industriesCardsBox[1]?.backgroundImage?.data?.attributes?.format?.large?.url || industriesCardsBox[1]?.backgroundImage?.data?.attributes?.formats?.large?.url || industriesCardsBox[1]?.backgroundImage?.data?.attributes?.url})`,
  } as React.CSSProperties;

  const thirdBoxBgImage = {
    '--third-box-bg-image': `url(${industriesCardsBox[2]?.backgroundImage?.data?.attributes?.format?.large?.url || industriesCardsBox[2]?.backgroundImage?.data?.attributes?.formats?.large?.url || industriesCardsBox[2]?.backgroundImage?.data?.attributes?.url})`,
  } as React.CSSProperties;

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {isMobile ? (
        <section className={styles.embla}>
          <div className={styles.embla__viewport} ref={emblaRef}>
            <div className={styles.embla__container}>
              <div className={styles.embla__slide_mobile_container}>
                <Link
                  href={`${industriesCardsBox[0]?.button?.link}`}
                  prefetch={false}
                  className={styles.mobile_box_link}
                >
                  <div
                    style={firstBoxBgImage}
                    className={styles.embla__slide_mobileOne}
                  ></div>
                  <div className={styles.embla__slide_mobile}>
                    <Heading
                      headingType="h2"
                      title={title}
                      className={[styles.title, styles.titleForMobile].join(
                        ' ',
                      )}
                    />
                    <div className={styles.contentWrapperForMobile}>
                      <Heading
                        headingType="h3"
                        title={industriesCardsBox[0]?.title}
                        className={styles.industriesCardsBoxTitle}
                      />
                      <div className={styles.descAndButtonWrapperForMobile}>
                        <div
                          className={styles.industriesCardsBoxDescription}
                          // eslint-disable-next-line react/no-danger
                          dangerouslySetInnerHTML={{
                            __html: industriesCardsBox[0]?.description,
                          }}
                        />
                        <CircularButtonWithArrow variant="small" />
                      </div>
                    </div>
                  </div>
                </Link>
                <div
                  style={{ padding: '30px 0' }}
                  className={emblastyles.embla__dots}
                >
                  {scrollSnaps.length > 1 &&
                    scrollSnaps.map((_, ind) => (
                      <DotButton
                        key={ind}
                        onClick={() => onDotButtonClick(ind)}
                        className={emblastyles.embla__dot.concat(
                          ind === selectedIndex
                            ? `${` ${emblastyles.embla__dot_selected}`}`
                            : '',
                        )}
                      />
                    ))}
                </div>
              </div>
              <div className={styles.embla__slide_mobile_container}>
                <Link
                  href={`${industriesCardsBox[1]?.button?.link}`}
                  prefetch={false}
                  className={styles.mobile_box_link}
                >
                  <div
                    style={secondBoxBgImage}
                    className={styles.embla__slide_mobileTwo}
                  ></div>
                  <div className={styles.embla__slide_mobile}>
                    <Heading
                      headingType="h2"
                      title={title}
                      className={[styles.title, styles.titleForMobile].join(
                        ' ',
                      )}
                    />
                    <div className={styles.contentWrapperForMobile}>
                      <Heading
                        headingType="h3"
                        title={industriesCardsBox[1]?.title}
                        className={styles.industriesCardsBoxTitle}
                      />
                      <div className={styles.descAndButtonWrapperForMobile}>
                        <div
                          className={styles.industriesCardsBoxDescription}
                          // eslint-disable-next-line react/no-danger
                          dangerouslySetInnerHTML={{
                            __html: industriesCardsBox[1]?.description,
                          }}
                        />
                        <CircularButtonWithArrow variant="small" />
                      </div>
                    </div>
                  </div>
                </Link>
                <div
                  style={{ padding: '30px 0' }}
                  className={emblastyles.embla__dots}
                >
                  {scrollSnaps.length > 1 &&
                    scrollSnaps.map((_, ind) => (
                      <DotButton
                        key={ind}
                        onClick={() => onDotButtonClick(ind)}
                        className={emblastyles.embla__dot.concat(
                          ind === selectedIndex
                            ? `${` ${emblastyles.embla__dot_selected}`}`
                            : '',
                        )}
                      />
                    ))}
                </div>
              </div>
              <div className={styles.embla__slide_mobile_container}>
                <Link
                  href={`${industriesCardsBox[2]?.button?.link}`}
                  prefetch={false}
                  className={styles.mobile_box_link}
                >
                  <div
                    style={thirdBoxBgImage}
                    className={styles.embla__slide_mobileThree}
                  ></div>
                  <div className={styles.embla__slide_mobile}>
                    <Heading
                      headingType="h2"
                      title={title}
                      className={[styles.title, styles.titleForMobile].join(
                        ' ',
                      )}
                    />
                    <div className={styles.contentWrapperForMobile}>
                      <Heading
                        headingType="h3"
                        title={industriesCardsBox[2]?.title}
                        className={styles.industriesCardsBoxTitle}
                      />
                      <div className={styles.descAndButtonWrapperForMobile}>
                        <div
                          className={styles.industriesCardsBoxDescription}
                          // eslint-disable-next-line react/no-danger
                          dangerouslySetInnerHTML={{
                            __html: industriesCardsBox[2]?.description,
                          }}
                        />
                        <CircularButtonWithArrow variant="small" />
                      </div>
                    </div>
                  </div>
                </Link>
                <div
                  style={{ padding: '30px 0' }}
                  className={emblastyles.embla__dots}
                >
                  {scrollSnaps.length > 1 &&
                    scrollSnaps.map((_, ind) => (
                      <DotButton
                        key={ind}
                        onClick={() => onDotButtonClick(ind)}
                        className={emblastyles.embla__dot.concat(
                          ind === selectedIndex
                            ? `${` ${emblastyles.embla__dot_selected}`}`
                            : '',
                        )}
                      />
                    ))}
                </div>
              </div>
            </div>
          </div>
        </section>
      ) : (
        <Container fluid style={firstBoxBgImage} className={styles.container}>
          <Heading headingType="h2" title={title} className={styles.title} />
          <div className={classNames(styles.box, styles.boxOne)}>
            <Link
              href={`${industriesCardsBox[0]?.button?.link}`}
              prefetch={false}
              className={styles.link}
              key={industriesCardsBox[0]?.id}
            >
              <div className={styles.contentWrapper}>
                <Heading
                  headingType="h3"
                  title={industriesCardsBox[0]?.title}
                  className={styles.industriesCardsBoxTitle}
                />
                <div className={styles.descAndButtonWrapper}>
                  <div
                    className={styles.industriesCardsBoxDescription}
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: industriesCardsBox[0]?.description,
                    }}
                  />
                  <CircularButtonWithArrow variant="small" />
                </div>
              </div>
            </Link>
          </div>
          <div
            style={secondBoxBgImage}
            className={classNames(styles.box, styles.boxTwo)}
          >
            <Link
              href={`${industriesCardsBox[1]?.button?.link}`}
              prefetch={false}
              className={styles.link}
              key={industriesCardsBox[1]?.id}
            >
              <div className={styles.contentWrapper}>
                <Heading
                  headingType="h3"
                  title={industriesCardsBox[1]?.title}
                  className={styles.industriesCardsBoxTitle}
                />
                <div className={styles.descAndButtonWrapper}>
                  <div
                    className={styles.industriesCardsBoxDescription}
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: industriesCardsBox[1]?.description,
                    }}
                  />
                  <CircularButtonWithArrow variant="small" />
                </div>
              </div>
            </Link>
          </div>
          <div
            style={thirdBoxBgImage}
            className={classNames(styles.box, styles.boxThree)}
          >
            <Link
              href={`${industriesCardsBox[2]?.button?.link}`}
              prefetch={false}
              className={styles.link}
              key={industriesCardsBox[2]?.id}
            >
              <div className={styles.contentWrapper}>
                <Heading
                  headingType="h3"
                  title={industriesCardsBox[2]?.title}
                  className={styles.industriesCardsBoxTitle}
                />
                <div className={styles.descAndButtonWrapper}>
                  <div
                    className={styles.industriesCardsBoxDescription}
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: industriesCardsBox[2]?.description,
                    }}
                  />
                  <CircularButtonWithArrow variant="small" />
                </div>
              </div>
            </Link>
          </div>
        </Container>
      )}
    </>
  );
}
