@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorThree, fifteenSpace, grayBorder from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm-450, breakpoint-sm from breakpoints;

.statisticsCardContainer {
  display: flex;
  flex-direction: column;
  gap: 40px;
  background-color: colorBlack;
  color: colorWhite;
  padding: 5rem 124px;

  @media screen and (max-width: 1120px) {
    padding: 5rem 32px;
  }

  @media screen and (max-width: 768px) {
    padding: 2.5rem 1rem;
  }
}

.componentTitleWrapper {
  text-align: center;
}

.componentTitle>h2 {
  font-size: 40px;
  line-height: 56px;
  font-weight: 600;
  color: colorWhite;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.boxWrapper_container {
  display: flex;
  justify-content: center;
}

.boxWrapper {
  display: flex;
  justify-content: center;
  gap: 20px;
  max-width: 1190px;

  @media (max-width: 1011px) {
    flex-wrap: wrap;
  }
}

.wrapper {
  position: relative;
}

.box {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px 8px;
  background-color: colorBlack;
  max-width: 13.875rem;
  height: 100%;
  transition: 0.4s ease-in-out all;
  position: relative;
  z-index: 1;

  @media screen and (min-width: 1024px) {
    max-width: 384px;
  }
}

.gradientWrapper {
  position: absolute;
  left: 0;
  bottom: -25%;
  width: 100%;
  height: 80%;
  background: url('https://cdn.marutitech.com/Gradient_6450d21812.svg') no-repeat center center;
  background-size: cover;
  z-index: 0;
}

.topSection {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: 768px) {
    align-items: center;
  }
}

.statistics {
  min-height: 62.4062px;
  font-size: 52px;
  line-height: 62.4px;
  font-weight: 600;
  color: colorWhite;
}

.statisticsDescription {
  margin: 0;
  display: block;
  display: -webkit-box;
  font-size: 16px;
  line-height: 23.04px;
  font-weight: 400;
  color: colorWhite;
  min-height: 70px;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: 768px) {
    text-align: center;
  }
}

.placeHolder {
  display: block;
  height: 800px;
}

.box_title>h3 {
  color: colorWhite;

  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;

  @media (max-width: 768px) {
    text-align: center;
  }
}