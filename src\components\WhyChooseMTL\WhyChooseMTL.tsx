'use client';

import React from 'react';
import Heading from '@components/Heading';
import useMediaQueryState from '@hooks/useMediaQueryState';

import { WhyChooseMTLTypes } from './types';
import styles from './WhyChooseMTL.module.css';

export default function WhyChooseMTL({ data }: WhyChooseMTLTypes) {
  const isMobile = useMediaQueryState({
    query: `(max-width: 585px)`,
  });
  const isTablet = useMediaQueryState({
    query: `(max-width: 867px)`,
  });
  return (
    <section className={styles.sectionWrapper}>
      <div className={styles.whyChooseMTLWrapper}>
        <div className={styles.whyChooseMTLContentArea}>
          <div className={styles.headingArea}>
            <Heading headingType="h2" title={data?.title} position="center" />

            <div
              className={styles.subHeading}
              dangerouslySetInnerHTML={{ __html: data?.subtitle }}
            />
          </div>
          <div className={styles.pointsWrapper}>
            {data?.whyChooseMtlCards?.map((card, index) => (
              <React.Fragment key={card?.id}>
                <div className={styles.pointCard}>
                  <div className={styles.pointCardContent}>
                    <Heading
                      headingType="h3"
                      richTextValue={card?.cardTitle}
                      className={styles.pointCardTitle}
                    />

                    <div
                      className={styles.pointCardDescription}
                      dangerouslySetInnerHTML={{
                        __html: card?.cardDescription,
                      }}
                    />
                  </div>
                </div>
                {!isMobile &&
                  (isTablet
                    ? index % 2 === 0 && <div className={styles.dividerLine} />
                    : index !== 2 &&
                      index !== 5 && <div className={styles.dividerLine} />)}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
