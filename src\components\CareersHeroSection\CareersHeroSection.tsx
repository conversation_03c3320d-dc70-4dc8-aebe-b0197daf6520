'use client';

import { useEffect, useRef, useState } from 'react';
import style from './CareersHeroSection.module.css';
import RichText from '@components/RichText';
import Button from '@components/Button';

export default function CareersHeroSection({ CareersHeroSectionData }) {
  const iframeRef = useRef(null);
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !loaded) {
          iframeRef.current.src = iframeRef.current.dataset.src;
          setLoaded(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold: 0.1 },
    );

    if (iframeRef.current) {
      observer.observe(iframeRef.current);
    }

    return () => {
      if (iframeRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  return (
    <section className={style.sectionWrapper}>
      <RichText richTextData={CareersHeroSectionData} />
      <Button
        className={style.button}
        label={CareersHeroSectionData?.button?.title}
        type="button"
        onClick={() =>
          window.open(CareersHeroSectionData?.button?.link, '_blank')
        }
      />
      <div className={style.videoWrapper}>
        <iframe
          ref={iframeRef}
          className={style.vimeoVideoIframe}
          data-src={`${CareersHeroSectionData?.vimeoVideoLink}?autoplay=1&enablejsapi=1&rel=0`}
          title="Vimeo video player"
          allow="autoplay; encrypted-media; fullscreen; picture-in-picture"
        ></iframe>
      </div>
    </section>
  );
}
