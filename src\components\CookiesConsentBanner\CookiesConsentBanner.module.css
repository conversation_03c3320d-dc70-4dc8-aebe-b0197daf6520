@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1024 from breakpoints;

.container {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  align-items: center;
  background-color: #59595990;
  color: colorWhite;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 1111;

  @media (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 0px;
  }
}

.content {
  font-size: 12px;
}

.buttonsWrapper {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.primaryButton {
  padding: 0px !important;
  height: 45px;
  width: 135px;
}

.secondaryButton {
  cursor: pointer;
  font-weight: 600;
  width: 111px;
}

.close {
  cursor: pointer;

  @media (max-width: breakpoint-xl-1024) {
    position: absolute;
    top: 7px;
    right: 5px;
  }
}
