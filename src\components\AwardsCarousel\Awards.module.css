@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md from breakpoints;

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 80px;
  --slide-size: auto;
  pointer-events: none;
}

.embla__viewport {
  overflow-x: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
  opacity: 0.5;
}

@media screen and (max-width: breakpoint-md) {
  .embla {
    margin-top: 1rem;
    margin-bottom: 3.75rem;
    --slide-spacing: 50px;
  }
}
